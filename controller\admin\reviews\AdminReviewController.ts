import { Request, Response } from "express";
import { ReviewService } from "../../../service/ReviewsService";
import asyncHandler from "../../../middleware/trycatch";
import { response, responseData } from "../../../utils/response";
interface AuthenticatedRequest extends Request {
  user?: {
    id: number;
    [key: string]: any;
  };
}

export class AdminReviewController {
  private reviewService = new ReviewService();

  getAllReviews = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.reviewService.getAllReviews(req.query);
    return responseData(res, 200, "Reviews fetched successfully", result);
  });

  getReviewById = asyncHandler(async (req: Request, res: Response) => {
    try {
      const result = await this.reviewService.getReviewById(Number(req.params.id));
      return responseData(res, 200, "Review fetched successfully", result);
    } catch (error: any) {
      if (error.message.includes("does not exist") || error.message.includes("not found")) {
        return response(res, 404, error.message);
      }
      console.error("Error fetching review:", error);
      return response(res, 500, "Failed to fetch review");
    }
  });

  updateReviewStatus = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
 
    let status = req.body.status || req.body.Status || req.query.status;
    const note = req.body.note || req.body.Note || req.query.note;
    
    if (!status && req.body) {
      if (typeof req.body === 'string') {
        try {
          const parsed = JSON.parse(req.body);
          status = parsed.status || parsed.Status;
        } catch (e) {
          console.log("Failed to parse body as JSON:", e);
        }
      }
      
      Object.keys(req.body).forEach(key => {
        console.log(`Body property: ${key} = ${req.body[key]} (type: ${typeof req.body[key]})`);
        if (key.toLowerCase().includes('status')) {
          status = req.body[key];
        }
      });
    }
    
    if (!status || status.trim() === '') {
      console.log("❌ Status validation failed:", { status, bodyKeys: Object.keys(req.body) });
      return response(res, 400, "Status is required in request body. Please ensure the 'status' field is provided.");
    }
    
    const statusData = {
      status: status.trim(),
      note: note ? note.trim() : undefined
    };
    
    console.log("✅ Processed status data:", statusData);
    
    try {
      await this.reviewService.updateReviewStatus(
        Number(req.params.id),
        statusData,
        req.user?.id || 1
      );
      return response(res, 200, "Review status updated successfully");
    } catch (error) {
      console.error("❌ Service error:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to update review status";
      return response(res, 500, errorMessage);
    }
  });

  deleteReview = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      await this.reviewService.deleteReview(Number(req.params.id), req.user?.id || 1);
      return response(res, 200, "Review deleted successfully");
    } catch (error: any) {
      if (error.message.includes("does not exist")) {
        return response(res, 404, error.message);
      }
      console.error("Error deleting review:", error);
      return response(res, 500, "Failed to delete review");
    }
  });

  getReviewsStats = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.reviewService.getReviewStats();
    return responseData(res, 200, "Review statistics fetched successfully", result);
  });

  flagReview = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { flagged = true } = req.body;
      await this.reviewService.flagReview(Number(req.params.id), flagged);
      return response(res, 200, `Review ${flagged ? 'flagged' : 'unflagged'} successfully`);
    } catch (error: any) {
      if (error.message.includes("does not exist")) {
        return response(res, 404, error.message);
      }
      console.error("Error flagging review:", error);
      return response(res, 500, "Failed to flag review");
    }
  });

  hideReview = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      const { reason } = req.body;
      await this.reviewService.hideReview(Number(req.params.id), reason, req.user?.id || 1);
      return response(res, 200, "Review hidden successfully");
    } catch (error: any) {
      if (error.message.includes("does not exist")) {
        return response(res, 404, error.message);
      }
      console.error("Error hiding review:", error);
      return response(res, 500, "Failed to hide review");
    }
  });

  restoreReview = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      await this.reviewService.restoreReview(Number(req.params.id), req.user?.id || 1);
      return response(res, 200, "Review restored successfully");
    } catch (error: any) {
      if (error.message.includes("does not exist")) {
        return response(res, 404, error.message);
      }
      console.error("Error restoring review:", error);
      return response(res, 500, "Failed to restore review");
    }
  });

  addReviewNote = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      const reviewId = Number(req.params.id);
      const { note } = req.body;

      await this.reviewService.addReviewNote(reviewId, note, req.user?.id || 1);
      return response(res, 201, "Note added successfully");
    } catch (error: any) {
      if (error.message.includes("does not exist") || error.message.includes("not found")) {
        return response(res, 404, error.message);
      }
      console.error("Error adding note:", error);
      return response(res, 500, "Failed to add note");
    }
  });

  getReviewNotes = asyncHandler(async (req: Request, res: Response) => {
    try {
      const reviewId = Number(req.params.id);
      const result = await this.reviewService.getReviewNotes(reviewId);
      return responseData(res, 200, "Review notes fetched successfully", result);
    } catch (error: any) {
      if (error.message.includes("does not exist") || error.message.includes("not found")) {
        return response(res, 404, error.message);
      }
      console.error("Error fetching review notes:", error);
      return response(res, 500, "Failed to fetch review notes");
    }
  });

  getReviewHistory = asyncHandler(async (req: Request, res: Response) => {
    try {
      const reviewId = Number(req.params.id);
      const result = await this.reviewService.getReviewHistory(reviewId);
      return responseData(res, 200, "Review history fetched successfully", result);
    } catch (error: any) {
      if (error.message.includes("does not exist") || error.message.includes("not found")) {
        return response(res, 404, error.message);
      }
      console.error("Error fetching review history:", error);
      return response(res, 500, "Failed to fetch review history");
    }
  });
}
