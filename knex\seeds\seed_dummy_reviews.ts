import { <PERSON><PERSON> } from "knex";
import bcrypt from "bcryptjs";

export async function seed(knex: Knex): Promise<void> {
  console.log("Starting to seed dummy reviews data...");

  try {
    // Create dummy user profiles first
    const profiles = [
      // Agents (Individual accounts)
      {
        firstName: "<PERSON>", lastName: "<PERSON>", email: "<EMAIL>", 
        phone: "+************", accountType: "Individual"
      },
      {
        firstName: "Fatima", lastName: "<PERSON>", email: "<EMAIL>", 
        phone: "+************", accountType: "Individual"
      },
      {
        firstName: "<PERSON>", lastName: "<PERSON>", email: "<EMAIL>", 
        phone: "+************", accountType: "Individual"
      },
      {
        firstName: "Aisha", lastName: "<PERSON>", email: "<EMAIL>", 
        phone: "+************", accountType: "Individual"
      },
      {
        firstName: "<PERSON>", lastName: "<PERSON>", email: "<EMAIL>", 
        phone: "+************", accountType: "Individual"
      },
      // Agencies (Company accounts)
      {
        firstName: "Real Estate", lastName: "Pro LLC", email: "<EMAIL>", 
        phone: "+************", accountType: "Company/Agency/PropertyDeveloper"
      },
      {
        firstName: "Dubai", lastName: "Properties", email: "<EMAIL>", 
        phone: "+************", accountType: "Company/Agency/PropertyDeveloper"
      },
      // Clients (who will write reviews)
      {
        firstName: "Sarah", lastName: "Johnson", email: "<EMAIL>", 
        phone: "+************", accountType: "Individual"
      },
      {
        firstName: "Michael", lastName: "Smith", email: "<EMAIL>", 
        phone: "+************", accountType: "Individual"
      },
      {
        firstName: "Priya", lastName: "Sharma", email: "<EMAIL>", 
        phone: "+************", accountType: "Individual"
      }
    ];

    const createdProfiles = [];
    const hashedPassword = await bcrypt.hash("password123", 10);

    // Insert profiles and create login accounts
    for (const profile of profiles) {
      // Check if profile already exists
      const existingProfile = await knex("prf.profile")
        .where("email", profile.email)
        .first();

      let profileId;
      if (existingProfile) {
        profileId = existingProfile.id;
        console.log(`Profile ${profile.email} already exists, using existing ID: ${profileId}`);
      } else {
        // Insert new profile
        const [insertedProfile] = await knex("prf.profile")
          .insert({
            firstName: profile.firstName,
            lastName: profile.lastName,
            email: profile.email,
            phone: profile.phone,
            accountType: profile.accountType,
            cardHolderName: "Test",
            cardType: "Test",
            cardNumber: "**********",
            createdOn: new Date()
          })
          .returning("*");
        
        profileId = insertedProfile.id;
        console.log(`Created new profile: ${profile.email} with ID: ${profileId}`);

        // Create login account for this profile
        const [loginAccount] = await knex("sec.login")
          .insert({
            profileId: profileId,
            username: profile.email.split('@')[0],
            accountType: "email",
            passwordHash: hashedPassword
          })
          .returning("*");

        // Assign role (assuming role ID 2 for regular users)
        await knex("sec.login_role")
          .insert({
            loginId: loginAccount.id,
            roleId: 2,
            createdBy: profileId
          });
      }

      createdProfiles.push({ ...profile, id: profileId });
    }

    // Clear existing reviews to avoid duplicates
    await knex("agn.review_history").del();
    await knex("agn.review_notes").del();
    await knex("agn.reviews").del();

    console.log("Cleared existing reviews data");

    // Create dummy reviews
    const reviews = [
      // Reviews for Ahmed Khan (Individual Agent)
      {
        reviewerId: createdProfiles[7].id, // Sarah Johnson
        revieweeId: createdProfiles[0].id, // Ahmed Khan
        reviewText: "Ahmed was extremely professional and helped me find the perfect apartment in Dubai Marina. His knowledge of the area is exceptional and he was always available to answer my questions. Highly recommended!",
        rating: 5,
        statusId: 30 // Confirmed
      },
      {
        reviewerId: createdProfiles[8].id, // Michael Smith
        revieweeId: createdProfiles[0].id, // Ahmed Khan
        reviewText: "Great experience working with Ahmed. He understood my requirements perfectly and showed me several properties that matched my criteria. The process was smooth and efficient.",
        rating: 4,
        statusId: 30 // Confirmed
      },
      {
        reviewerId: createdProfiles[9].id, // Priya Sharma
        revieweeId: createdProfiles[0].id, // Ahmed Khan
        reviewText: "Ahmed is a reliable agent who goes above and beyond for his clients. He helped me negotiate a great deal on my new home. Very satisfied with his service.",
        rating: 5,
        statusId: 30 // Confirmed
      },

      // Reviews for Fatima Ali (Individual Agent)
      {
        reviewerId: createdProfiles[7].id, // Sarah Johnson
        revieweeId: createdProfiles[1].id, // Fatima Ali
        reviewText: "Fatima was wonderful to work with. She has excellent communication skills and really listened to what I was looking for. Found me a great investment property in JLT.",
        rating: 5,
        statusId: 30 // Confirmed
      },
      {
        reviewerId: createdProfiles[8].id, // Michael Smith
        revieweeId: createdProfiles[1].id, // Fatima Ali
        reviewText: "Professional and knowledgeable. Fatima helped me understand the Dubai real estate market as a first-time buyer. Very patient and helpful throughout the process.",
        rating: 4,
        statusId: 30 // Confirmed
      },

      // Reviews for Mohammed Hassan (Individual Agent)
      {
        reviewerId: createdProfiles[9].id, // Priya Sharma
        revieweeId: createdProfiles[2].id, // Mohammed Hassan
        reviewText: "Mohammed has deep expertise in luxury properties. He showed me some amazing villas in Emirates Hills and helped me make an informed decision. Excellent service!",
        rating: 5,
        statusId: 30 // Confirmed
      },
      {
        reviewerId: createdProfiles[7].id, // Sarah Johnson
        revieweeId: createdProfiles[2].id, // Mohammed Hassan
        reviewText: "Good agent with solid knowledge of high-end properties. The viewing process was well organized and he provided valuable market insights.",
        rating: 4,
        statusId: 30 // Confirmed
      },

      // Reviews for Real Estate Pro LLC (Agency)
      {
        reviewerId: createdProfiles[8].id, // Michael Smith
        revieweeId: createdProfiles[5].id, // Real Estate Pro LLC
        reviewText: "Real Estate Pro LLC has an excellent team. They handled my property sale efficiently and got me a great price. Their marketing strategy was very effective.",
        rating: 5,
        statusId: 30 // Confirmed
      },
      {
        reviewerId: createdProfiles[9].id, // Priya Sharma
        revieweeId: createdProfiles[5].id, // Real Estate Pro LLC
        reviewText: "Professional agency with good market knowledge. They helped me rent out my apartment quickly and handled all the paperwork smoothly.",
        rating: 4,
        statusId: 30 // Confirmed
      },

      // Reviews for Dubai Properties (Agency)
      {
        reviewerId: createdProfiles[7].id, // Sarah Johnson
        revieweeId: createdProfiles[6].id, // Dubai Properties
        reviewText: "Dubai Properties has a wide portfolio and their agents are very knowledgeable. They helped me find a commercial space for my business. Great experience overall.",
        rating: 4,
        statusId: 30 // Confirmed
      },

      // Some pending/hidden reviews for variety
      {
        reviewerId: createdProfiles[8].id, // Michael Smith
        revieweeId: createdProfiles[3].id, // Aisha Ahmed
        reviewText: "This review is under moderation. The agent was okay but there were some delays in communication.",
        rating: 3,
        statusId: 3 // Pending
      },
      {
        reviewerId: createdProfiles[9].id, // Priya Sharma
        revieweeId: createdProfiles[4].id, // Omar Ibrahim
        reviewText: "This review was hidden due to inappropriate content.",
        rating: 2,
        statusId: 31, // Hidden
        hideReason: "Contains inappropriate language"
      }
    ];

    // Get login ID for created_by field (using first profile's login)
    const adminLogin = await knex("sec.login")
      .where("profileId", createdProfiles[0].id)
      .first();

    const createdBy = adminLogin ? adminLogin.id : 1;

    // Insert reviews
    const createdReviews = [];
    for (const review of reviews) {
      const [insertedReview] = await knex("agn.reviews")
        .insert({
          ...review,
          createdBy: createdBy,
          created_at: new Date(),
          updated_at: new Date()
        })
        .returning("*");
      
      createdReviews.push(insertedReview);
      console.log(`Created review ID: ${insertedReview.id} from ${review.reviewerId} to ${review.revieweeId}`);
    }

    // Add some review notes for admin moderation
    const reviewNotes = [
      {
        reviewId: createdReviews[10].id, // The pending review
        note: "Review is under moderation due to potential bias. Investigating reviewer credentials.",
        createdBy: createdBy
      },
      {
        reviewId: createdReviews[11].id, // The hidden review
        note: "Review hidden due to inappropriate language and potential defamation. Reviewer has been notified.",
        createdBy: createdBy
      }
    ];

    for (const note of reviewNotes) {
      await knex("agn.review_notes")
        .insert({
          ...note,
          created_at: new Date()
        });
      console.log(`Added note for review ID: ${note.reviewId}`);
    }

    // Add review history entries
    const reviewHistory = [
      {
        reviewId: createdReviews[10].id, // The pending review
        action: "Status Changed",
        previousStatus: 30, // Confirmed
        newStatus: 3, // Pending
        notes: "Review flagged for manual review due to suspicious activity",
        createdBy: createdBy
      },
      {
        reviewId: createdReviews[11].id, // The hidden review
        action: "Status Changed",
        previousStatus: 30, // Confirmed
        newStatus: 31, // Hidden
        notes: "Review hidden due to policy violation",
        createdBy: createdBy
      }
    ];

    for (const history of reviewHistory) {
      await knex("agn.review_history")
        .insert({
          ...history,
          created_at: new Date()
        });
      console.log(`Added history entry for review ID: ${history.reviewId}`);
    }

    console.log("✅ Successfully seeded dummy reviews data!");
    console.log(`Created ${createdProfiles.length} profiles`);
    console.log(`Created ${createdReviews.length} reviews`);
    console.log(`Created ${reviewNotes.length} review notes`);
    console.log(`Created ${reviewHistory.length} history entries`);

  } catch (error) {
    console.error("❌ Error seeding dummy reviews data:", error);
    throw error;
  }
}
