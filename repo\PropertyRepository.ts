import { PoolClient } from "pg";
import db from "../config/database";
import { GetAllPropertiesResponseDTO } from "../dto/property/GetAllPropertiesResponseDTO";
import { PaginationDTO } from "../dto/property/PaginationDTO";
import { PropertyDTO } from "../dto/property/PropertyDTO";
import { StatusCountDTO } from "../dto/property/StatusCountDTO";
import { PropertyQueries } from "../utils/database/queries/PropertyQueries";

export class PropertyRepository {
  async getFilteredProperties(
    query: any
  ): Promise<GetAllPropertiesResponseDTO> {
    const {
      page = 1,
      pageSize = 10,
      status,
      propertyTypeId,
      locationId,
      listingType,
    } = query;

    const limit = parseInt(String(pageSize), 10) || 10;
    const currentPage = parseInt(String(page), 10) || 1;

    let statusId: number | null = null;
    if (status && String(status).trim().toLowerCase() !== "all") {
      const statusName = String(status).trim();
      const { rows: statusRow } = await db.query(
        PropertyQueries.FIND_STATUS_ID_BY_NAME,
        [statusName]
      );
      if (statusRow.length > 0) {
        statusId = statusRow[0].id;
      }
    }

    const params = [
      statusId,
      propertyTypeId ? Number(propertyTypeId) : null,
      locationId ? Number(locationId) : null,
      listingType ? Number(listingType) : null,
      limit,
      currentPage,
    ];

    const { rows } = await db.query(
      PropertyQueries.GET_FILTERED_PROPERTIES,
      params
    );

    const { rows: statusCountsRows } = await db.query(
      PropertyQueries.GET_PROPERTY_STATUS_COUNTS,
      [
        propertyTypeId ? Number(propertyTypeId) : null,
        locationId ? Number(locationId) : null,
        listingType ? Number(listingType) : null,
      ]
    );

    const statusCounts: StatusCountDTO[] = statusCountsRows.map((row) => ({
      status_id: row.status_id,
      status_name: row.status_name,
      count: row.count,
    }));

    if (!rows.length) {
      return {
        properties: [],
        pagination: {
          total: 0,
          totalPages: 0,
          currentPage,
          perPage: limit,
        },
        statusCounts,
      };
    }

    const { total_count, total_pages } = rows[0];
    const properties: PropertyDTO[] = rows.map(
      ({ total_count, total_pages, ...rest }) => rest
    );

    const pagination: PaginationDTO = {
      total: total_count,
      totalPages: total_pages,
      currentPage,
      perPage: limit,
    };

    return {
      properties,
      pagination,
      statusCounts,
    };
  }

  async getPropertyById(id: number) {
    const { rows } = await db.query(PropertyQueries.GET_PROPERTY_DETAIL_BY_ID, [
      id,
    ]);
    return rows[0] as PropertyDTO;
  }

  async savePropertyWithAssets(
    data: any,
    imageKeys: string[],
    features: string[] | string,
    statusId: number,
    loginId: number,
    client: PoolClient
  ): Promise<PropertyDTO> {
    // -------- Step 1: Create or Update Property --------
    const fields = [
      "id",
      "code",
      "name",
      "local",
      "agencyId",
      "propertyTypeId",
      "apartmentTypeId",
      "totalRooms",
      "locationId",
      "address",
      "currencyId",
      "price",
      "size",
      "permitNo",
      "parking",
      "swimmingPools",
      "gym",
      "startDate",
      "statusId",
      "createdBy",
      "isFeatured",
      "isVerified",
      "adminNote",
      "expiryDate",
      "listingType",
      "completionStatus",
      "ownershipTypeId",
      "slug",
      "metaTitle",
      "metaDescription",
      "bedrooms",
      "bathrooms",
      "furnished",
      "permitId",
      "unitNo",
      "govtIssuedQr",
      "projectId",
      "tagLine",
    ];

    const params = fields.map((field) => data[field] ?? null);

    const { rows } = await client.query(
      PropertyQueries.CREATE_OR_UPDATE_PROPERTY(fields.length),
      params
    );
    const property = rows[0] as PropertyDTO;

    if (!property?.id) {
      throw new Error("Property creation failed: ID is missing.");
    }

    // -------- Step 2: Insert Images --------
    if (Array.isArray(imageKeys) && imageKeys.length > 0) {
      const inserts = imageKeys.map((key) =>
        client.query(PropertyQueries.INSERT_PROPERTY_IMAGE, [
          property.id,
          key,
          statusId,
          48, // mediaTypeId
          loginId,
        ])
      );
      await Promise.all(inserts);
    }

    // -------- Step 3: Insert Features --------
    const validFeatures = Array.isArray(features)
      ? features
      : typeof features === "string"
      ? String(features)
          .split(",")
          .map((f) => f.trim())
          .filter(Boolean)
      : [];

    const inserts = [];

    for (const feature of validFeatures) {
      const { rows: existing } = await client.query(
        PropertyQueries.CHECK_EXISTING_FEATURE,
        [property.id, feature]
      );

      if (existing.length === 0) {
        inserts.push(
          client.query(PropertyQueries.INSERT_FEATURE, [
            property.id,
            feature,
            statusId,
            data.createdBy,
          ])
        );
      }
    }

    await Promise.all(inserts);

    return property;
  }

  async updateStatus(id: number, status: string): Promise<void> {
    const { rows } = await db.query(PropertyQueries.FIND_STATUS_ID_BY_NAME, [
      status,
    ]);
    if (!rows.length) throw new Error(`Status '${status}' not found.`);
    const statusId = rows[0].id;

    const result = await db.query(PropertyQueries.UPDATE_PROPERTY_STATUS, [
      statusId,
      id,
    ]);
    if (result.rowCount === 0)
      throw new Error("Property not found or update failed");
  }

  async toggleFlag(id: number, column: string): Promise<string> {
    const allowed = ["isFeatured", "isVerified"];
    if (!allowed.includes(column)) throw new Error("Invalid column name");

    const result = await db.query(PropertyQueries.GET_PROPERTY_FLAG(column), [
      id,
    ]);
    
    if (result.rowCount === 0) throw new Error("Property not found");

    const currentValue = result.rows[0][column];
    const newValue = !currentValue;

    await db.query(PropertyQueries.UPDATE_PROPERTY_FLAG(column), [
      newValue,
      id,
    ]);

    return `The property’s '${
      column === "isFeatured" ? "Featured" : "Verified"
    }' status has been successfully toggled to '${
      newValue ? "Enabled" : "Disabled"
    }'.`;
  }

  async deleteProperty(id: number): Promise<void> {
    const result = await db.query(PropertyQueries.DELETE_PROPERTY_BY_ID, [id]);
    if (result.rowCount === 0)
      throw new Error("Property not found or already deleted");
  }

  async updatePhotos(req: any): Promise<PropertyDTO | null> {
    return this.getPropertyById(Number(req.params.propertyId));
  }
}
