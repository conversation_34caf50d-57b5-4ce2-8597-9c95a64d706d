import { TABLE } from "../table";

export const STATUS = {
  GET_ALL_STATUS: `SELECT * FROM "${TABLE.STATUSES}" ORDER BY name;`,
  GET_STATUS_BY_NAME: `SELECT id FROM "${TABLE.STATUSES}" WHERE LOWER(name) = LOWER($1) LIMIT 1`,
  GET_HIDDEN_STATUS: `SELECT id FROM "${TABLE.STATUSES}" WHERE LOWER(name) = LOWER('Hidden') LIMIT 1`,
  GET_PENDING_STATUS: `SELECT id FROM "${TABLE.STATUSES}" WHERE LOWER(name) = LOWER('Pending') LIMIT 1`,
  GET_APPROVED_STATUS: `SELECT id FROM "${TABLE.STATUSES}" WHERE LOWER(name) = LOWER('Approved') LIMIT 1`,
  SEARCH_AGENCIES: `
    SELECT 
      a.id AS "companyId",
      a.name AS "companyName",
      a."companyEmail",
      a."companyPhone",
      p.id AS "profileId",
      p."statusId",
      profile_status.name AS "profileStatus"
    FROM "${TABLE.AGENCIES}" a
    JOIN "${TABLE.PROFILE_TABLE}" p ON a."profileId" = p."id"
    LEFT JOIN "${TABLE.STATUSES}" AS profile_status ON profile_status.id = p."statusId"
    WHERE LOWER(a.name) LIKE '%' || LOWER($1) || '%'
    ORDER BY a.name;
  `,
};
