import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Add reply functionality to reviews table
  await knex.schema.withSchema('agn').alterTable('reviews', function(table) {
    table.text('replyText').nullable().comment('Agent reply to the review');
    table.timestamp('replyDate').nullable().comment('When the reply was added');
    table.integer('repliedBy').unsigned().nullable().comment('Agent who replied');
    
    // Add flagged reason functionality
    table.text('flaggedReason').nullable().comment('Reason for flagging the review');
    table.integer('flaggedBy').unsigned().nullable().comment('Agent who flagged the review');
    table.timestamp('flaggedAt').nullable().comment('When the review was flagged');
    table.text('additionalDetails').nullable().comment('Additional details about the flag');
    
    // Add foreign key constraint for repliedBy
    table.foreign('repliedBy').references('id').inTable('prf.profile').onDelete('SET NULL');
    
    // Add foreign key constraint for flaggedBy
    table.foreign('flaggedBy').references('id').inTable('prf.profile').onDelete('SET NULL');
    
    // Add index for better performance
    table.index('repliedBy', 'idx_reviews_replied_by');
    table.index('flaggedBy', 'idx_reviews_flagged_by');
    table.index('flagged', 'idx_reviews_flagged');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.withSchema('agn').alterTable('reviews', function(table) {
    // Drop flagged reason related columns
    table.dropForeign(['flaggedBy']);
    table.dropIndex(['flaggedBy'], 'idx_reviews_flagged_by');
    table.dropIndex(['flagged'], 'idx_reviews_flagged');
    table.dropColumn('flaggedReason');
    table.dropColumn('flaggedBy');
    table.dropColumn('flaggedAt');
    table.dropColumn('additionalDetails');
    
    // Drop reply related columns
    table.dropForeign(['repliedBy']);
    table.dropIndex(['repliedBy'], 'idx_reviews_replied_by');
    table.dropColumn('replyText');
    table.dropColumn('replyDate');
    table.dropColumn('repliedBy');
  });
}
