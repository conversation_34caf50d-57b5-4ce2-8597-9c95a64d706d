import { ReviewRepository } from "../repo/ReviewsRepository";
import { NoteRepository } from "../repo/NoteRepository";
import { ReviewDTO } from "../dto/reviews/ReviewDTO";
import { GetAllReviewsResponseDTO } from "../dto/reviews/GetAllReviewsResponseDTO";
import { GetAllReviewsQueryDTO } from "../dto/reviews/GetAllReviewsQueryDTO";
import { ReviewStatsDTO } from "../dto/reviews/ReviewStatsDTO";
import { CreateReviewDTO, UpdateReviewStatusDTO, BulkUpdateReviewsDTO } from "../dto/reviews/CreateUpdateReviewDTO";
import db from "../config/database";
import { AUTH } from "../utils/database/queries/auth";
import { STATUS } from "../utils/database/queries/status";

export class ReviewService {
    private reviewRepo = new ReviewRepository();
    private noteRepo = new NoteRepository();

    async getAllReviews(query: GetAllReviewsQueryDTO): Promise<GetAllReviewsResponseDTO> {
        const {
            page = 1,
            limit = 10,
            status,
            search,
            type, // New filter parameter: 'agent' or 'agency'
        } = query;

        // Validate type parameter if provided
        if (type && !['agent', 'agency'].includes(type)) {
            throw new Error("Invalid type parameter. Must be 'agent' or 'agency'");
        }

        return this.reviewRepo.getAllReviews(
            parseInt(page.toString()),
            parseInt(limit.toString()),
            status,
            search,
            type
        );
    }

    async getReviewById(id: number): Promise<ReviewDTO> {
        const review = await this.reviewRepo.getReviewById(id);
        if (!review) {
            throw new Error("Review not found");
        }
        return review;
    }

    async createReview(
        reviewerId: number,
        revieweeId: number,
        reviewText: string,
        rating: number
    ): Promise<ReviewDTO> {
        if (!revieweeId || !reviewText || !rating) {
            throw new Error("Missing required fields: revieweeId, reviewText, and rating");
        }

        if (rating < 1 || rating > 5) {
            throw new Error("Rating must be between 1 and 5");
        }

        if (reviewerId === revieweeId) {
            throw new Error("You cannot review yourself");
        }

        const revieweeExists = await db.query(AUTH.SELECT_BY_ID, [revieweeId]);
        if (!revieweeExists.rows.length) {
            throw new Error("Reviewee not found");
        }

        const reviewee = revieweeExists.rows[0];

        if (!['Individual', 'Company/Agency/PropertyDeveloper'].includes(reviewee.accountType)) {
            throw new Error("You can only review agents or agencies");
        }

        const existingReview = await this.reviewRepo.checkExistingReview(reviewerId, revieweeId);
        if (existingReview) {
            throw new Error("You have already reviewed this profile");
        }

        const reviewerLogin = await db.query(
            `SELECT id FROM sec.login WHERE "profileId" = $1`,
            [reviewerId]
        );

        if (!reviewerLogin.rows.length) {
            throw new Error("Reviewer login not found");
        }

        const pendingStatusQuery = await db.query(STATUS.GET_PENDING_STATUS);

        if (!pendingStatusQuery.rows.length) {
            throw new Error("Pending status not found in database");
        }

        const pendingStatusId = pendingStatusQuery.rows[0].id;

        return this.reviewRepo.createReview(
            reviewerId,
            revieweeId,
            reviewText,
            rating,
            pendingStatusId,
            reviewerLogin.rows[0].id
        );
    }

    async updateReviewStatus(id: number, statusData: UpdateReviewStatusDTO, adminId: number): Promise<void> {
        if (!statusData || typeof statusData !== 'object') {
            throw new Error("Status data is required and must be an object");
        }
        
        if (!statusData.status || typeof statusData.status !== 'string') {
            throw new Error("Status is required and must be a string");
        }

        const status = statusData.status.trim();
        if (!status) {
            throw new Error("Status cannot be empty");
        }

        let statusId: number;
        
        try {
            statusId = await this.reviewRepo.getStatusIdByName(status);
        } catch (error) {
            // Fallback to correct status IDs based on seeds
            switch (status.toLowerCase()) {
                case 'approved':
                case 'approve':
                case 'published':
                case 'publish':
                case 'confirmed':
                    statusId = 30; // Confirmed status ID from seeds (used as Approved for reviews)
                    break;
                case 'rejected':
                case 'reject':
                    statusId = 8; // Rejected status ID from seeds
                    break;
                case 'pending':
                    statusId = 3; // Pending status ID from seeds
                    break;
                case 'deleted':
                case 'hidden':
                    statusId = 31; // Hidden status ID from seeds
                    break;
                default:
                    throw new Error("Invalid status");
            }
        }

        const currentStatus = await this.reviewRepo.getCurrentReviewStatus(id);

        await this.reviewRepo.updateReviewStatus(id, statusId, adminId);

        await this.reviewRepo.logReviewAction(
            id,
            'status_update',
            currentStatus,
            statusId,
            statusData.note || null,
            adminId
        );

        if (statusData.note) {
            await this.addReviewNote(id, statusData.note, adminId);
        }
    }

    async deleteReview(id: number, modifiedBy: number): Promise<void> {
        await this.reviewRepo.deleteReview(id, modifiedBy);
    }

    async getReviewStats(): Promise<ReviewStatsDTO> {
        return this.reviewRepo.getReviewStats();
    }

    async getProfileReviews(profileId: number, page: number = 1, limit: number = 10): Promise<GetAllReviewsResponseDTO> {
        return this.reviewRepo.getProfileReviews(profileId, page, limit);
    }

    async getProfileRatingStats(profileId: number) {
        return this.reviewRepo.getProfileRatingStats(profileId);
    }

    async getUserReceivedReviews(userId: number, page: number = 1, limit: number = 10): Promise<GetAllReviewsResponseDTO> {
        return this.reviewRepo.getUserReceivedReviews(userId, page, limit);
    }

    async getAgentReviews(agentId: number, page: number = 1, limit: number = 10, includeAll: boolean = false, filter: string = 'all', search: string = ''): Promise<GetAllReviewsResponseDTO> {
        // Validate that the agent exists and is eligible to receive reviews
        const agentExists = await db.query(AUTH.SELECT_BY_ID, [agentId]);
        if (!agentExists.rows.length) {
            throw new Error("Agent not found");
        }

        const agent = agentExists.rows[0];
        
        // Check if the profile is eligible to receive reviews (Individual or Company/Agency)
        if (!['Individual', 'Company/Agency/PropertyDeveloper'].includes(agent.accountType)) {
            throw new Error("Invalid agent - only individual agents and agencies can receive reviews");
        }

        // includeAll = true means the agent is viewing their own reviews (show all statuses)
        // includeAll = false means public view (show only approved reviews)
        return this.reviewRepo.getAgentReviews(agentId, page, limit, includeAll, filter, search);
    }

    async flagReview(id: number, flagged: boolean = true, reason?: string, flaggedBy?: number, additionalDetails?: string): Promise<void> {
        await this.reviewRepo.flagReview(id, flagged, reason, flaggedBy, additionalDetails);
    }

    async addReviewNote(reviewId: number, note: string, adminId: number): Promise<void> {
        if (!note || note.trim().length === 0) {
            throw new Error("Note cannot be empty");
        }
        await this.reviewRepo.addReviewNote(reviewId, note, adminId);
    }

    async getReviewNotes(reviewId: number) {
        return this.reviewRepo.getReviewNotes(reviewId);
    }

    async getReviewHistory(reviewId: number) {
        return this.reviewRepo.getReviewHistory(reviewId);
    }

    async hideReview(id: number, reason: string, adminId: number): Promise<void> {
        const hiddenStatusId = await this.reviewRepo.getHiddenStatusId();
        
        const currentStatus = await this.reviewRepo.getCurrentReviewStatus(id);
        
        await this.reviewRepo.updateReviewStatus(id, hiddenStatusId, adminId);
        
        await this.reviewRepo.logReviewAction(
            id,
            'hide',
            currentStatus,
            hiddenStatusId,
            reason,
            adminId
        );
    }

    async restoreReview(id: number, adminId: number): Promise<void> {
        // First try to get the status from the hide action's previousStatus field
        let previousStatusId = await this.reviewRepo.getPreviousStatusBeforeHidden(id);
        
        // If that doesn't work, try to get the last status before the review was hidden
        if (!previousStatusId) {
            previousStatusId = await this.reviewRepo.getLastStatusBeforeHidden(id);
        }
        
        if (!previousStatusId) {
            throw new Error("Cannot restore review: No previous status found before it was hidden");
        }

        const currentStatus = await this.reviewRepo.getCurrentReviewStatus(id);
        
        await this.reviewRepo.updateReviewStatus(id, previousStatusId, adminId);
        
        await this.reviewRepo.logReviewAction(
            id,
            'restore',
            currentStatus,
            previousStatusId,
            'Review restored',
            adminId
        );
        
        await this.addReviewNote(id, "Review restored", adminId);
    }

    // Reply functionality methods
    async addReviewReply(reviewId: number, replyText: string, agentId: number) {
        // Check if review exists and belongs to agent
        const review = await this.getReviewById(reviewId);
        if (review.revieweeId !== agentId) {
            throw new Error("You can only reply to reviews about yourself");
        }

        // Check if reply already exists
        if (review.replyText) {
            throw new Error("Review already has a reply");
        }

        const result = await this.reviewRepo.addReviewReply(reviewId, replyText, agentId);
        
        // Add to history
        await this.reviewRepo.logReviewAction(
            reviewId,
            'reply_added',
            0,
            0,
            'Agent added reply to review',
            agentId
        );

        return result;
    }

    async updateReviewReply(reviewId: number, replyText: string, agentId: number) {
        // Check if review exists and belongs to agent (agent is the reviewee)
        const review = await this.getReviewById(reviewId);
        if (review.revieweeId !== agentId) {
            throw new Error("You can only update replies to reviews about yourself");
        }

        // Check if reply exists
        if (!review.replyText) {
            throw new Error("No reply found to update. Please add a reply first.");
        }

        const result = await this.reviewRepo.updateReviewReply(reviewId, replyText, agentId);
        
        // Add to history
        await this.reviewRepo.logReviewAction(
            reviewId,
            'reply_updated',
            0,
            0,
            'Agent updated reply to review',
            agentId
        );

        return result;
    }

    async deleteReviewReply(reviewId: number, agentId: number) {
        // Check if review exists and belongs to agent (agent is the reviewee)
        const review = await this.getReviewById(reviewId);
        if (review.revieweeId !== agentId) {
            throw new Error("You can only delete replies to reviews about yourself");
        }

        // Check if reply exists
        if (!review.replyText) {
            throw new Error("No reply found to delete");
        }

        await this.reviewRepo.deleteReviewReply(reviewId);
        
        // Add to history
        await this.reviewRepo.logReviewAction(
            reviewId,
            'reply_deleted',
            0,
            0,
            'Agent deleted reply to review',
            agentId
        );
    }
}
