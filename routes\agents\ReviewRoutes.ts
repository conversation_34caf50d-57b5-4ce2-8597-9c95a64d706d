import express from "express";
import { authMiddleware } from "../../middleware/authMiddleware";
import { ReviewController } from "../../controller/agents/ReviewController";
import { storageData } from "../../utils/services/multer";
import {
  validateBasicFlagRequest,
  validateUnflagRequest,
  validateReviewId
} from "../../middleware/flagReviewValidation";

const reviewController = new ReviewController();
const router = express.Router();
const upload = storageData("reviews");

router.use(authMiddleware);
router.get("/my-reviews", reviewController.getAgentReviews);
router.get("/stats", reviewController.getMyReviewStats);
router.get("/:id", reviewController.getReviewById);
router.post("/", upload.none(), reviewController.createReview);
router.patch("/:id/flag", validateReviewId, validateBasicFlagRequest, upload.none(), reviewController.flagReview);
router.get("/flag-reasons", reviewController.getFlagReasons);
router.post("/:id/unflag", validateReviewId, validateUnflagRequest, upload.none(), reviewController.unflagReview);
router.post("/:id/reply", upload.none(), reviewController.replyToReview);
router.put("/:id/reply", upload.none(), reviewController.updateReviewReply);
router.delete("/:id/reply", reviewController.deleteReviewReply);

export default router;
