import { TABLE } from "../table";

export const REVIEWS = {
  CREATE_REVIEW: `
    WITH inserted_review AS (
      INSERT INTO agn.${TABLE.REVIEW} (
        "reviewerId", "revieweeId", "reviewText", "rating", "statusId", "createdBy"
      ) VALUES ($1, $2, $3, $4, $5, $6) 
      RETURNING *
    )
    SELECT 
      r.*,
      reviewer."firstName" AS "reviewerFirstName",
      reviewer."lastName" AS "reviewerLastName",
      reviewer.email AS "reviewerEmail",
      reviewee."firstName" AS "revieweeFirstName",
      reviewee."lastName" AS "revieweeLastName",
      reviewee."accountType" AS "revieweeType",
      CASE 
        WHEN reviewee."accountType" = 'Company/Agency/PropertyDeveloper' THEN agn.name
        ELSE NULL
      END AS "agencyName"
    FROM inserted_review r
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewer ON r."reviewerId" = reviewer.id
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewee ON r."revieweeId" = reviewee.id
    LEFT JOIN agn.${TABLE.AGENCIES} agn ON r."revieweeId" = agn."profileId"
  `,

  GET_ALL_REVIEWS: `
    SELECT 
      r.id,
      r."reviewerId",
      r."revieweeId",
      r."reviewText",
      r.rating,
      r."statusId",
      r."hideReason",
      r."flagged",
      r."created_at",
      r."updated_at",
      
      -- Reply fields
      r."replyText",
      r."replyDate",
      r."repliedBy",
      
      reviewer."firstName" AS "reviewerFirstName",
      reviewer."lastName" AS "reviewerLastName",
      reviewer.email AS "reviewerEmail",
      
      reviewee."firstName" AS "revieweeFirstName",
      reviewee."lastName" AS "revieweeLastName",
      reviewee.email AS "revieweeEmail",
      reviewee."accountType" AS "revieweeType",
      
      -- Agency name if reviewee is agency
      CASE 
        WHEN reviewee."accountType" = 'Company/Agency/PropertyDeveloper' THEN agn.name
        ELSE NULL
      END AS "agencyName",
      
      -- Status details
      s.name AS "statusName",
      
      -- Admin notes aggregated as JSON array
      COALESCE(
        (SELECT JSON_AGG(
          JSON_BUILD_OBJECT(
            'id', rn.id,
            'note', rn.note,
            'createdAt', rn."created_at",
            'adminName', CONCAT(admin."firstName", ' ', admin."lastName")
          )
          ORDER BY rn."created_at" DESC
        )
        FROM agn.review_notes rn
        LEFT JOIN sec.login l ON rn."createdBy" = l.id
        LEFT JOIN prf.${TABLE.PROFILE_TABLE} admin ON l."profileId" = admin.id
        WHERE rn."reviewId" = r.id),
        '[]'::json
      ) AS "notes",
      
      -- Review history aggregated as JSON array
      COALESCE(
        (SELECT JSON_AGG(
          JSON_BUILD_OBJECT(
            'id', rh.id,
            'action', rh.action,
            'previousStatus', rh."previousStatus",
            'newStatus', rh."newStatus",
            'previousStatusName', prev_status.name,
            'newStatusName', new_status.name,
            'notes', rh.notes,
            'createdAt', rh."created_at",
            'adminName', CONCAT(hist_admin."firstName", ' ', hist_admin."lastName")
          )
          ORDER BY rh."created_at" DESC
        )
        FROM agn.review_history rh
        LEFT JOIN sec.login hist_login ON rh."createdBy" = hist_login.id
        LEFT JOIN prf.${TABLE.PROFILE_TABLE} hist_admin ON hist_login."profileId" = hist_admin.id
        LEFT JOIN look.${TABLE.STATUS} prev_status ON rh."previousStatus" = prev_status.id
        LEFT JOIN look.${TABLE.STATUS} new_status ON rh."newStatus" = new_status.id
        WHERE rh."reviewId" = r.id),
        '[]'::json
      ) AS "history"
      
    FROM agn.${TABLE.REVIEW} r
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewer ON r."reviewerId" = reviewer.id
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewee ON r."revieweeId" = reviewee.id
    LEFT JOIN agn.${TABLE.AGENCIES} agn ON r."revieweeId" = agn."profileId"
    LEFT JOIN look.${TABLE.STATUS} s ON r."statusId" = s.id
    ORDER BY r."created_at" DESC
    LIMIT $1 OFFSET $2
  `,

  GET_REVIEWS_COUNT: `
    SELECT COUNT(*) as total 
    FROM agn.${TABLE.REVIEW}
  `,

  GET_REVIEWS_BY_STATUS: `
    SELECT 
      r.id,
      r."reviewerId",
      r."revieweeId",
      r."reviewText",
      r.rating,
      r."statusId",
      r."hideReason",
      r."flagged",
      r."created_at",
      r."updated_at",
      
      -- Reviewer details
      reviewer."firstName" AS "reviewerFirstName",
      reviewer."lastName" AS "reviewerLastName",
      reviewer.email AS "reviewerEmail",
      
      -- Reviewee details
      reviewee."firstName" AS "revieweeFirstName",
      reviewee."lastName" AS "revieweeLastName",
      reviewee.email AS "revieweeEmail",
      reviewee."accountType" AS "revieweeType",
      
      -- Agency name if reviewee is agency
      CASE 
        WHEN reviewee."accountType" = 'Company/Agency/PropertyDeveloper' THEN agn.name
        ELSE NULL
      END AS "agencyName",
      
      -- Status details
      s.name AS "statusName",
      
      -- Admin notes aggregated as JSON array
      COALESCE(
        (SELECT JSON_AGG(
          JSON_BUILD_OBJECT(
            'id', rn.id,
            'note', rn.note,
            'createdAt', rn."created_at",
            'adminName', CONCAT(admin."firstName", ' ', admin."lastName")
          )
          ORDER BY rn."created_at" DESC
        )
        FROM agn.review_notes rn
        LEFT JOIN sec.login l ON rn."createdBy" = l.id
        LEFT JOIN prf.${TABLE.PROFILE_TABLE} admin ON l."profileId" = admin.id
        WHERE rn."reviewId" = r.id),
        '[]'::json
      ) AS "notes"
      
    FROM agn.${TABLE.REVIEW} r
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewer ON r."reviewerId" = reviewer.id
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewee ON r."revieweeId" = reviewee.id
    LEFT JOIN agn.${TABLE.AGENCIES} agn ON r."revieweeId" = agn."profileId"
    LEFT JOIN look.${TABLE.STATUS} s ON r."statusId" = s.id
    WHERE r."statusId" = $1
    ORDER BY r."created_at" DESC
    LIMIT $2 OFFSET $3
  `,

  GET_REVIEW_BY_ID: `
    SELECT 
      r.id,
      r."reviewerId",
      r."revieweeId",
      r."reviewText",
      r.rating,
      r."statusId",
      r."hideReason",
      r."flagged",
      r."created_at",
      r."updated_at",
      
      -- Reply fields
      r."replyText",
      r."replyDate",
      r."repliedBy",
      
      -- Reviewer details
      reviewer."firstName" AS "reviewerFirstName",
      reviewer."lastName" AS "reviewerLastName",
      reviewer.email AS "reviewerEmail",
      
      -- Reviewee details
      reviewee."firstName" AS "revieweeFirstName",
      reviewee."lastName" AS "revieweeLastName",
      reviewee.email AS "revieweeEmail",
      reviewee."accountType" AS "revieweeType",
      
      -- Agency name if reviewee is agency
      CASE 
        WHEN reviewee."accountType" = 'Company/Agency/PropertyDeveloper' THEN agn.name
        ELSE NULL
      END AS "agencyName",
      
      -- Status details
      s.name AS "statusName"
      
    FROM agn.${TABLE.REVIEW} r
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewer ON r."reviewerId" = reviewer.id
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewee ON r."revieweeId" = reviewee.id
    LEFT JOIN agn.${TABLE.AGENCIES} agn ON r."revieweeId" = agn."profileId"
    LEFT JOIN look.${TABLE.STATUS} s ON r."statusId" = s.id
    WHERE r.id = $1
  `,

  UPDATE_REVIEW_STATUS: `
    UPDATE agn.${TABLE.REVIEW} 
    SET "statusId" = $1, "modifiedBy" = $2, "updated_at" = CURRENT_TIMESTAMP
    WHERE id = $3
    RETURNING *
  `,
UPDATE_REVIEW_STATUS_FOR_HIDDEN: `
    UPDATE agn.${TABLE.REVIEW} 
    SET "statusId" = $1, "modifiedBy" = $2, "updated_at" = CURRENT_TIMESTAMP
    WHERE id = $3
    RETURNING *
  `,
  UPDATE_REVIEW_STATUS_WITH_REASON: `
    UPDATE agn.${TABLE.REVIEW} 
    SET "statusId" = $1, "modifiedBy" = $2, "updated_at" = CURRENT_TIMESTAMP, "hideReason" = $4
    WHERE id = $3
    RETURNING *
  `,

  ADD_REVIEW_NOTE: `
    INSERT INTO agn.review_notes (
      "reviewId", "note", "createdBy", "created_at"
    ) VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
    RETURNING *
  `,

  GET_REVIEW_NOTES: `
    SELECT 
      n.id,
      n.note,
      n."created_at",
      admin."firstName" AS "adminFirstName",
      admin."lastName" AS "adminLastName"
    FROM agn.review_notes n
    LEFT JOIN sec.login l ON n."createdBy" = l.id
    LEFT JOIN prf.profile admin ON l."profileId" = admin.id
    WHERE n."reviewId" = $1
    ORDER BY n."created_at" DESC
  `,

  GET_REVIEW_HISTORY: `
    SELECT 
      h.id,
      h.action,
      h."previousStatus",
      h."newStatus",
      h.notes,
      h."created_at",
      admin."firstName" AS "adminFirstName",
      admin."lastName" AS "adminLastName"
    FROM agn.review_history h
    LEFT JOIN sec.login l ON h."createdBy" = l.id
    LEFT JOIN prf.profile admin ON l."profileId" = admin.id
    WHERE h."reviewId" = $1
    ORDER BY h."created_at" DESC
  `,

  LOG_REVIEW_ACTION: `
    INSERT INTO agn.review_history (
      "reviewId", action, "previousStatus", "newStatus", notes, "createdBy", "created_at"
    ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
    RETURNING *
  `,

  GET_PREVIOUS_STATUS_BEFORE_HIDDEN: `
    SELECT h."previousStatus"
    FROM agn.review_history h
    WHERE h."reviewId" = $1
      AND (h.action = 'hide' OR (h.action = 'status_update' AND h."newStatus" = 31))
    ORDER BY h."created_at" DESC
    LIMIT 1
  `,

  GET_LAST_STATUS_BEFORE_HIDDEN: `
    SELECT h."newStatus" as "statusId"
    FROM agn.review_history h
    WHERE h."reviewId" = $1
      AND h.action IN ('status_update', 'create')
      AND h."created_at" < (
        SELECT MAX(created_at)
        FROM agn.review_history
        WHERE "reviewId" = $1 AND (action = 'hide' OR (action = 'status_update' AND "newStatus" = 31))
      )
    ORDER BY h."created_at" DESC
    LIMIT 1
  `,

  GET_APPROVED_REVIEWS_FOR_PROFILE: `
    SELECT
      r.id,
      r."reviewText",
      r.rating,
      r."created_at",
      reviewer."firstName" AS "reviewerFirstName",
      reviewer."lastName" AS "reviewerLastName",
      reviewer.email AS "reviewerEmail"
    FROM agn.${TABLE.REVIEW} r
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewer ON r."reviewerId" = reviewer.id
    WHERE r."revieweeId" = $1 AND r."statusId" = 30
    ORDER BY r."created_at" DESC
    LIMIT $2 OFFSET $3
  `,

  CHECK_EXISTING_REVIEW: `
    SELECT id FROM agn.${TABLE.REVIEW} 
    WHERE "reviewerId" = $1 AND "revieweeId" = $2
  `,

  GET_PROFILE_RATING_STATS: `
    SELECT
      COUNT(*) as "totalReviews",
      ROUND(AVG(rating), 2) as "averageRating",
      COUNT(CASE WHEN "replyText" IS NOT NULL AND "replyText" != '' THEN 1 END) as "repliedCount",
      COUNT(CASE WHEN "flagged" = true THEN 1 END) as "flaggedCount"
    FROM agn.${TABLE.REVIEW}
    WHERE "revieweeId" = $1 AND "statusId" = 30
  `,

  DELETE_REVIEW: `
    DELETE FROM agn.${TABLE.REVIEW} 
    WHERE id = $1
    RETURNING *
  `,

  GET_REVIEWS_COUNT_BY_STATUS: `
    SELECT COUNT(*) as total 
    FROM agn.${TABLE.REVIEW}
    WHERE "statusId" = $1
  `,

  SEARCH_REVIEWS: `
    SELECT 
      r.id,
      r."reviewerId",
      r."revieweeId",
      r."reviewText",
      r.rating,
      r."statusId",
      r."flagged",
      r."created_at",
      r."updated_at",
      
      -- Reviewer details
      reviewer."firstName" AS "reviewerFirstName",
      reviewer."lastName" AS "reviewerLastName",
      reviewer.email AS "reviewerEmail",
      
      -- Reviewee details
      reviewee."firstName" AS "revieweeFirstName",
      reviewee."lastName" AS "revieweeLastName",
      reviewee.email AS "revieweeEmail",
      reviewee."accountType" AS "revieweeType",
      
      -- Agency name if reviewee is agency
      CASE 
        WHEN reviewee."accountType" = 'Company/Agency/PropertyDeveloper' THEN agn.name
        ELSE NULL
      END AS "agencyName",
      
      -- Status details
      s.name AS "statusName",
      
      -- Admin notes aggregated as JSON array
      COALESCE(
        (SELECT JSON_AGG(
          JSON_BUILD_OBJECT(
            'id', rn.id,
            'note', rn.note,
            'createdAt', rn."created_at",
            'adminName', CONCAT(admin."firstName", ' ', admin."lastName")
          )
          ORDER BY rn."created_at" DESC
        )
        FROM agn.review_notes rn
        LEFT JOIN sec.login l ON rn."createdBy" = l.id
        LEFT JOIN prf.${TABLE.PROFILE_TABLE} admin ON l."profileId" = admin.id
        WHERE rn."reviewId" = r.id),
        '[]'::json
      ) AS "notes"
      
    FROM agn.${TABLE.REVIEW} r
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewer ON r."reviewerId" = reviewer.id
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewee ON r."revieweeId" = reviewee.id
    LEFT JOIN agn.${TABLE.AGENCIES} agn ON r."revieweeId" = agn."profileId"
    LEFT JOIN look.${TABLE.STATUS} s ON r."statusId" = s.id
    WHERE 
      LOWER(CONCAT(reviewer."firstName", ' ', reviewer."lastName")) LIKE LOWER($1)
      OR LOWER(reviewer.email) LIKE LOWER($1)
      OR LOWER(CONCAT(reviewee."firstName", ' ', reviewee."lastName")) LIKE LOWER($1)
      OR LOWER(reviewee.email) LIKE LOWER($1)
      OR LOWER(agn.name) LIKE LOWER($1)
    ORDER BY r."created_at" DESC
    LIMIT $2 OFFSET $3
  `,

  SEARCH_REVIEWS_BY_STATUS: `
    SELECT 
      r.id,
      r."reviewerId",
      r."revieweeId",
      r."reviewText",
      r.rating,
      r."statusId",
      r."flagged",
      r."created_at",
      r."updated_at",
      
      -- Reviewer details
      reviewer."firstName" AS "reviewerFirstName",
      reviewer."lastName" AS "reviewerLastName",
      reviewer.email AS "reviewerEmail",
      
      -- Reviewee details
      reviewee."firstName" AS "revieweeFirstName",
      reviewee."lastName" AS "revieweeLastName",
      reviewee.email AS "revieweeEmail",
      reviewee."accountType" AS "revieweeType",
      
      -- Agency name if reviewee is agency
      CASE 
        WHEN reviewee."accountType" = 'Company/Agency/PropertyDeveloper' THEN agn.name
        ELSE NULL
      END AS "agencyName",
      
      -- Status details
      s.name AS "statusName",
      
      -- Admin notes aggregated as JSON array
      COALESCE(
        (SELECT JSON_AGG(
          JSON_BUILD_OBJECT(
            'id', rn.id,
            'note', rn.note,
            'createdAt', rn."created_at",
            'adminName', CONCAT(admin."firstName", ' ', admin."lastName")
          )
          ORDER BY rn."created_at" DESC
        )
        FROM agn.review_notes rn
        LEFT JOIN sec.login l ON rn."createdBy" = l.id
        LEFT JOIN prf.${TABLE.PROFILE_TABLE} admin ON l."profileId" = admin.id
        WHERE rn."reviewId" = r.id),
        '[]'::json
      ) AS "notes"
      
    FROM agn.${TABLE.REVIEW} r
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewer ON r."reviewerId" = reviewer.id
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewee ON r."revieweeId" = reviewee.id
    LEFT JOIN agn.${TABLE.AGENCIES} agn ON r."revieweeId" = agn."profileId"
    LEFT JOIN look.${TABLE.STATUS} s ON r."statusId" = s.id
    WHERE r."statusId" = $2 AND (
      LOWER(CONCAT(reviewer."firstName", ' ', reviewer."lastName")) LIKE LOWER($1)
      OR LOWER(reviewer.email) LIKE LOWER($1)
      OR LOWER(CONCAT(reviewee."firstName", ' ', reviewee."lastName")) LIKE LOWER($1)
      OR LOWER(reviewee.email) LIKE LOWER($1)
      OR LOWER(agn.name) LIKE LOWER($1)
    )
    ORDER BY r."created_at" DESC
    LIMIT $3 OFFSET $4
  `,

  GET_USER_REVIEWS: `
    SELECT 
      r.id,
      r."revieweeId",
      r."reviewText",
      r.rating,
      r."statusId",
      r."flagged",
      r."created_at",
      r."updated_at",
      
      -- Reply fields
      r."replyText",
      r."replyDate",
      r."repliedBy",
      
      -- Reviewer details (the user who wrote the review)
      reviewer."firstName" AS "reviewerFirstName",
      reviewer."lastName" AS "reviewerLastName",
      reviewer.email AS "reviewerEmail",
      
      -- Reviewee details
      reviewee."firstName" AS "revieweeFirstName",
      reviewee."lastName" AS "revieweeLastName",
      reviewee."accountType" AS "revieweeType",
      
      -- Agency name if reviewee is agency
      CASE 
        WHEN reviewee."accountType" = 'Company/Agency/PropertyDeveloper' THEN agn.name
        ELSE NULL
      END AS "agencyName",
      
      -- Status details
      s.name AS "statusName"
      
    FROM agn.${TABLE.REVIEW} r
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewer ON r."reviewerId" = reviewer.id
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewee ON r."revieweeId" = reviewee.id
    LEFT JOIN agn.${TABLE.AGENCIES} agn ON r."revieweeId" = agn."profileId"
    LEFT JOIN look.${TABLE.STATUS} s ON r."statusId" = s.id
    WHERE r."reviewerId" = $1
    ORDER BY r."created_at" DESC
    LIMIT $2 OFFSET $3
  `,

  GET_USER_RECEIVED_REVIEWS: `
    SELECT 
      r.id,
      r."reviewerId",
      r."reviewText",
      r.rating,
      r."statusId",
      r."flagged",
      r."created_at",
      r."updated_at",
      
      -- Reply fields
      r."replyText",
      r."replyDate",
      r."repliedBy",
      
      -- Reviewer details (the user who wrote the review)
      reviewer."firstName" AS "reviewerFirstName",
      reviewer."lastName" AS "reviewerLastName",
      reviewer.email AS "reviewerEmail",
      reviewer."accountType" AS "reviewerType",
      
      -- Reviewee details (the current user)
      reviewee."firstName" AS "revieweeFirstName",
      reviewee."lastName" AS "revieweeLastName",
      reviewee."accountType" AS "revieweeType",
      
      -- Agency name if reviewee is agency
      CASE 
        WHEN reviewee."accountType" = 'Company/Agency/PropertyDeveloper' THEN agn.name
        ELSE NULL
      END AS "agencyName",
      
      -- Status details
      s.name AS "statusName"
      
    FROM agn.${TABLE.REVIEW} r
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewer ON r."reviewerId" = reviewer.id
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewee ON r."revieweeId" = reviewee.id
    LEFT JOIN agn.${TABLE.AGENCIES} agn ON r."revieweeId" = agn."profileId"
    LEFT JOIN look.${TABLE.STATUS} s ON r."statusId" = s.id
    WHERE r."revieweeId" = $1 
      AND r."reviewerId" != $1
      AND s.name IN ('approved', 'confirmed')
    ORDER BY r."created_at" DESC
    LIMIT $2 OFFSET $3
  `,

  GET_ALL_REVIEWS_FOR_AGENT: `
    SELECT 
      r.id,
      r."reviewerId",
      r."revieweeId",
      r."reviewText",
      r.rating,
      r."statusId",
      r."hideReason",
      r."flagged",
      r."created_at",
      r."updated_at",
      
      -- Reviewer details
      reviewer."firstName" AS "reviewerFirstName",
      reviewer."lastName" AS "reviewerLastName",
      reviewer.email AS "reviewerEmail",
      reviewer."accountType" AS "reviewerType",
      
      -- Reviewee details (the agent)
      reviewee."firstName" AS "revieweeFirstName",
      reviewee."lastName" AS "revieweeLastName",
      reviewee."accountType" AS "revieweeType",
      
      -- Agency name if reviewee is agency
      CASE 
        WHEN reviewee."accountType" = 'Company/Agency/PropertyDeveloper' THEN agn.name
        ELSE NULL
      END AS "agencyName",
      
      -- Status details
      s.name AS "statusName",
      
      -- Admin notes aggregated as JSON array (for transparency)
      COALESCE(
        (SELECT JSON_AGG(
          JSON_BUILD_OBJECT(
            'id', rn.id,
            'note', rn.note,
            'createdAt', rn."created_at",
            'adminName', CONCAT(admin."firstName", ' ', admin."lastName")
          )
          ORDER BY rn."created_at" DESC
        )
        FROM agn.review_notes rn
        LEFT JOIN sec.login l ON rn."createdBy" = l.id
        LEFT JOIN prf.${TABLE.PROFILE_TABLE} admin ON l."profileId" = admin.id
        WHERE rn."reviewId" = r.id),
        '[]'::json
      ) AS "notes"
      
    FROM agn.${TABLE.REVIEW} r
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewer ON r."reviewerId" = reviewer.id
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewee ON r."revieweeId" = reviewee.id
    LEFT JOIN agn.${TABLE.AGENCIES} agn ON r."revieweeId" = agn."profileId"
    LEFT JOIN look.${TABLE.STATUS} s ON r."statusId" = s.id
    WHERE r."revieweeId" = $1
    ORDER BY r."created_at" DESC
    LIMIT $2 OFFSET $3
  `,

  GET_APPROVED_REVIEWS_FOR_AGENT: `
    SELECT
      r.id,
      r."reviewText",
      r.rating,
      r."created_at",
      r."updated_at",
      
      -- Reply fields
      r."replyText",
      r."replyDate",
      r."repliedBy",
      
      -- Reviewer details
      reviewer."firstName" AS "reviewerFirstName",
      reviewer."lastName" AS "reviewerLastName",
      reviewer.email AS "reviewerEmail",
      reviewer."accountType" AS "reviewerType",
      
      -- Reviewee details (the agent)
      reviewee."firstName" AS "revieweeFirstName",
      reviewee."lastName" AS "revieweeLastName",
      reviewee."accountType" AS "revieweeType",
      
      -- Agency name if reviewee is agency
      CASE 
        WHEN reviewee."accountType" = 'Company/Agency/PropertyDeveloper' THEN agn.name
        ELSE NULL
      END AS "agencyName"
      
    FROM agn.${TABLE.REVIEW} r
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewer ON r."reviewerId" = reviewer.id
    LEFT JOIN prf.${TABLE.PROFILE_TABLE} reviewee ON r."revieweeId" = reviewee.id
    LEFT JOIN agn.${TABLE.AGENCIES} agn ON r."revieweeId" = agn."profileId"
    WHERE r."revieweeId" = $1 AND r."statusId" = 30
    ORDER BY r."created_at" DESC
    LIMIT $2 OFFSET $3
  `
};
