import { Request, Response, NextFunction } from "express";
import { response } from "../utils/response";

interface AuthenticatedRequest extends Request {
  user?: {
    id: number;
    [key: string]: any;
  };
}

export const validateBasicFlagRequest = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  const { flagged, reason } = req.body;

  // Validate flagged parameter
  if (flagged !== undefined && typeof flagged !== "boolean") {
    response(res, 400, "Flagged parameter must be a boolean");
    return;
  }

  // If flagging (true or undefined which defaults to true), reason is required
  const isFlagging = flagged === undefined || flagged === true;
  if (isFlagging) {
    if (!reason || typeof reason !== "string" || reason.trim().length === 0) {
      response(res, 400, "Reason is required when flagging a review");
      return;
    }
    
    if (reason.trim().length > 500) {
      response(res, 400, "Reason cannot exceed 500 characters");
      return;
    }
  }

  next();
};

export const validateUnflagRequest = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  const { reason } = req.body;

  // Reason is optional for unflagging, but if provided, validate it
  if (reason) {
    if (typeof reason !== "string") {
      response(res, 400, "Reason must be a string");
      return;
    }
    
    if (reason.trim().length > 300) {
      response(res, 400, "Reason cannot exceed 300 characters");
      return;
    }
  }

  next();
};

export const validateReviewId = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  const reviewId = Number(req.params.id);

  if (!reviewId || isNaN(reviewId) || reviewId <= 0) {
    response(res, 400, "Invalid review ID provided");
    return;
  }

  next();
};
