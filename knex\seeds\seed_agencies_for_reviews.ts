import { Knex } from "knex";

export async function seed(knex: Knex): Promise<void> {
  console.log("Starting to seed agency data for reviews...");

  try {
    // Create agencies for Company/Agency/PropertyDeveloper type profiles
    const agencies = [
      {
        name: "Real Estate Pro LLC",
        description: "Leading real estate agency in Dubai specializing in luxury properties",
        email: "<EMAIL>",
        phone: "+971501234572",
        address: "Business Bay, Dubai, UAE",
        website: "www.realestatepro.com"
      },
      {
        name: "Dubai Properties",
        description: "Comprehensive property solutions for residential and commercial needs",
        email: "<EMAIL>", 
        phone: "+971501234573",
        address: "DIFC, Dubai, UAE",
        website: "www.dubaiproperties.com"
      },
      {
        name: "Emirates Real Estate",
        description: "Premium real estate services across UAE",
        email: "<EMAIL>",
        phone: "+971501234580",
        address: "Sheikh Zayed Road, Dubai, UAE", 
        website: "www.emiratesre.com"
      }
    ];

    // Find the company profiles we created in the reviews seed
    const companyProfiles = await knex("prf.profile")
      .whereIn("email", [
        "<EMAIL>",
        "<EMAIL>"
      ])
      .select("*");

    console.log(`Found ${companyProfiles.length} company profiles`);

    // Clear existing agency data
    await knex("agn.agency").del();
    console.log("Cleared existing agency data");

    // Insert agencies
    for (let i = 0; i < agencies.length; i++) {
      const agency = agencies[i];
      let profileId = null;

      // Find matching profile for first two agencies
      if (i < companyProfiles.length) {
        const matchingProfile = companyProfiles.find(p => p.email === agency.email);
        if (matchingProfile) {
          profileId = matchingProfile.id;
        }
      }

      const [insertedAgency] = await knex("agn.agency")
        .insert({
          profileId: profileId,
          name: agency.name,
          description: agency.description,
          email: agency.email,
          phone: agency.phone,
          address: agency.address,
          website: agency.website,
          statusId: 1, // Activated
          createdBy: profileId || 1,
          createdOn: new Date()
        })
        .returning("*");

      console.log(`Created agency: ${agency.name} with ID: ${insertedAgency.id}`);
    }

    console.log("✅ Successfully seeded agency data for reviews!");

  } catch (error) {
    console.error("❌ Error seeding agency data:", error);
    throw error;
  }
}
