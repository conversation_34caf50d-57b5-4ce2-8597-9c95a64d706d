import { PoolClient } from "pg";
import db from "../config/database";
import { REVIEWS } from "../utils/database/queries/reviews";
import { AUTH } from "../utils/database/queries/auth";
import { STATUS } from "../utils/database/queries/status";
import { ReviewDTO } from "../dto/reviews/ReviewDTO";
import { GetAllReviewsResponseDTO, PaginationDTO } from "../dto/reviews/GetAllReviewsResponseDTO";
import { ReviewStatsDTO } from "../dto/reviews/ReviewStatsDTO";

export class ReviewRepository {
  async getAllReviews(
    page: number = 1,
    limit: number = 10,
    status?: string,
    search?: string,
    type?: string // New parameter: 'agent' or 'agency'
  ): Promise<GetAllReviewsResponseDTO> {
    const offset = (page - 1) * limit;
    let reviews;
    let totalCount;

    // Build filter conditions
    const filters: string[] = [];
    const queryParams: any[] = [];
    let paramIndex = 1;

    // Handle search filter
    if (search) {
      filters.push(`(
        LOWER(CONCAT(reviewer."firstName", ' ', reviewer."lastName")) LIKE LOWER($${paramIndex})
        OR LOWER(reviewer.email) LIKE LOWER($${paramIndex})
        OR LOWER(CONCAT(reviewee."firstName", ' ', reviewee."lastName")) LIKE LOWER($${paramIndex})
        OR LOWER(reviewee.email) LIKE LOWER($${paramIndex})
        OR LOWER(agn.name) LIKE LOWER($${paramIndex})
      )`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    // Handle status filter
    if (status) {
      filters.push(`r."statusId" = $${paramIndex}`);
      // Convert status string to status ID
      let statusId: number;
      try {
        statusId = await this.getStatusIdByName(status);
      } catch (error) {
        // If status name is not found, try parsing as integer for backward compatibility
        const parsedStatus = parseInt(status);
        if (isNaN(parsedStatus)) {
          throw new Error(`Invalid status value: ${status}`);
        }
        statusId = parsedStatus;
      }
      queryParams.push(statusId);
      paramIndex++;
    }

    // Handle type filter
    if (type) {
      if (type.toLowerCase() === 'agent') {
        filters.push(`reviewee."accountType" = $${paramIndex}`);
        queryParams.push('Individual');
      } else if (type.toLowerCase() === 'agency') {
        filters.push(`reviewee."accountType" = $${paramIndex}`);
        queryParams.push('Company/Agency/PropertyDeveloper');
      }
      paramIndex++;
    }

    // Build WHERE clause
    const whereClause = filters.length > 0 ? `WHERE ${filters.join(' AND ')}` : '';

    // Add pagination parameters
    queryParams.push(limit, offset);
    const limitParam = `$${paramIndex}`;
    const offsetParam = `$${paramIndex + 1}`;

    // Execute main query
    if (filters.length > 0) {
      // Use dynamic query when filters are applied
      const mainQuery = `
        SELECT 
          r.id,
          r."reviewerId",
          r."revieweeId",
          r."reviewText",
          r.rating,
          r."statusId",
          r."hideReason",
          r."flagged",
          r."created_at",
          r."updated_at",
          
          reviewer."firstName" AS "reviewerFirstName",
          reviewer."lastName" AS "reviewerLastName",
          reviewer.email AS "reviewerEmail",
          
          reviewee."firstName" AS "revieweeFirstName",
          reviewee."lastName" AS "revieweeLastName",
          reviewee.email AS "revieweeEmail",
          reviewee."accountType" AS "revieweeType",
          
          CASE 
            WHEN reviewee."accountType" = 'Company/Agency/PropertyDeveloper' THEN agn.name
            ELSE NULL
          END AS "agencyName",
          
          s.name AS "statusName",
          
          COALESCE(
            (SELECT JSON_AGG(
              JSON_BUILD_OBJECT(
                'id', rn.id,
                'note', rn.note,
                'createdAt', rn."created_at",
                'adminName', CONCAT(admin."firstName", ' ', admin."lastName")
              )
              ORDER BY rn."created_at" DESC
            )
            FROM agn.review_notes rn
            LEFT JOIN sec.login l ON rn."createdBy" = l.id
            LEFT JOIN prf.profile admin ON l."profileId" = admin.id
            WHERE rn."reviewId" = r.id),
            '[]'::json
          ) AS "notes",
          
          COALESCE(
            (SELECT JSON_AGG(
              JSON_BUILD_OBJECT(
                'id', rh.id,
                'action', rh.action,
                'previousStatus', rh."previousStatus",
                'newStatus', rh."newStatus",
                'previousStatusName', prev_status.name,
                'newStatusName', new_status.name,
                'notes', rh.notes,
                'createdAt', rh."created_at",
                'adminName', CONCAT(hist_admin."firstName", ' ', hist_admin."lastName")
              )
              ORDER BY rh."created_at" DESC
            )
            FROM agn.review_history rh
            LEFT JOIN sec.login hist_login ON rh."createdBy" = hist_login.id
            LEFT JOIN prf.profile hist_admin ON hist_login."profileId" = hist_admin.id
            LEFT JOIN look.status prev_status ON rh."previousStatus" = prev_status.id
            LEFT JOIN look.status new_status ON rh."newStatus" = new_status.id
            WHERE rh."reviewId" = r.id),
            '[]'::json
          ) AS "history"
          
        FROM agn.reviews r
        LEFT JOIN prf.profile reviewer ON r."reviewerId" = reviewer.id
        LEFT JOIN prf.profile reviewee ON r."revieweeId" = reviewee.id
        LEFT JOIN agn.agencies agn ON r."revieweeId" = agn."profileId"
        LEFT JOIN look.status s ON r."statusId" = s.id
        ${whereClause}
        ORDER BY r."created_at" DESC
        LIMIT ${limitParam} OFFSET ${offsetParam}
      `;

      const countQuery = `
        SELECT COUNT(*) as total 
        FROM agn.reviews r
        LEFT JOIN prf.profile reviewer ON r."reviewerId" = reviewer.id
        LEFT JOIN prf.profile reviewee ON r."revieweeId" = reviewee.id
        LEFT JOIN agn.agencies agn ON r."revieweeId" = agn."profileId"
        ${whereClause}
      `;

      reviews = await db.query(mainQuery, queryParams);
      totalCount = await db.query(countQuery, queryParams.slice(0, -2)); // Remove limit and offset for count
    } else {
      // Use existing optimized query when no filters
      reviews = await db.query(REVIEWS.GET_ALL_REVIEWS, [limit, offset]);
      totalCount = await db.query(REVIEWS.GET_REVIEWS_COUNT);
    }

    const total = parseInt(totalCount.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    const pagination: PaginationDTO = {
      total,
      totalPages,
      currentPage: page,
      perPage: limit,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };

    return {
      reviews: reviews.rows,
      pagination,
    };
  }

  async getReviewById(id: number): Promise<ReviewDTO | null> {
    const { rows } = await db.query(REVIEWS.GET_REVIEW_BY_ID, [id]);
    return rows[0] || null;
  }

  async createReview(
    reviewerId: number,
    revieweeId: number,
    reviewText: string,
    rating: number,
    statusId: number,
    createdBy: number
  ): Promise<ReviewDTO> {
    const { rows } = await db.query(REVIEWS.CREATE_REVIEW, [
      reviewerId,
      revieweeId,
      reviewText,
      rating,
      statusId,
      createdBy,
    ]);
    return rows[0];
  }

  async updateReviewStatus(
    id: number,
    statusId: number,
    updatedBy: number
  ): Promise<void> {
    const result = await db.query(REVIEWS.UPDATE_REVIEW_STATUS, [statusId, updatedBy, id]);
    if (result.rowCount === 0) {
      throw new Error(`Review with id ${id} does not exist.`);
    }
  }

  async deleteReview(id: number, modifiedBy: number): Promise<void> {
    const result = await db.query(REVIEWS.DELETE_REVIEW, [id]);
    if (result.rowCount === 0) {
      throw new Error(`Review with id ${id} does not exist.`);
    }
  }

  async getReviewStats(): Promise<ReviewStatsDTO> {
    // Get status IDs dynamically instead of using hardcoded values
    const pendingStatusId = await this.getStatusIdByName('Pending');
    const approvedStatusId = await this.getStatusIdByName('Publish');
    const rejectedStatusId = await this.getStatusIdByName('Rejected');
    const hiddenStatusId = await this.getStatusIdByName('Hidden');

    // Use a single query with subqueries for better performance and consistency
    const statsQuery = await db.query(`
      SELECT
        (SELECT COUNT(*) FROM agn.reviews) as total_reviews,
        (SELECT COUNT(*) FROM agn.reviews WHERE "statusId" = $1) as pending_reviews,
        (SELECT COUNT(*) FROM agn.reviews WHERE "statusId" = $2) as approved_reviews,
        (SELECT COUNT(*) FROM agn.reviews WHERE "statusId" = $3) as rejected_reviews,
        (SELECT COUNT(*) FROM agn.reviews WHERE "statusId" = $4) as hidden_reviews,
        (SELECT COUNT(*) FROM agn.reviews WHERE "flagged" = true) as flagged_reviews,
        (SELECT AVG(rating) FROM agn.reviews) as avg_rating
    `, [pendingStatusId, approvedStatusId, rejectedStatusId, hiddenStatusId]);

    const ratingDistribution = await db.query(`
      SELECT rating, COUNT(*) as count
      FROM agn.reviews
      GROUP BY rating
      ORDER BY rating
    `);

    const stats = statsQuery.rows[0];

    return {
      totalReviews: parseInt(stats.total_reviews) || 0,
      pendingReviews: parseInt(stats.pending_reviews) || 0,
      approvedReviews: parseInt(stats.approved_reviews) || 0,
      rejectedReviews: parseInt(stats.rejected_reviews) || 0,
      hiddenReviews: parseInt(stats.hidden_reviews) || 0,
      flaggedReviews: parseInt(stats.flagged_reviews) || 0,
      averageRating: parseFloat(stats.avg_rating) || 0,
      ratingDistribution: ratingDistribution.rows.map((row: any) => ({
        rating: row.rating,
        count: parseInt(row.count)
      }))
    };
  }

  async bulkUpdateReviewsStatus(
    reviewIds: number[],
    statusId: number,
    updatedBy: number
  ): Promise<void> {
    await db.query(
      `UPDATE agn.reviews SET "statusId" = $1, "modifiedBy" = $2, "updated_at" = CURRENT_TIMESTAMP WHERE id = ANY($3)`,
      [statusId, updatedBy, reviewIds]
    );
  }

  async getProfileReviews(
    profileId: number,
    page: number = 1,
    limit: number = 10
  ): Promise<GetAllReviewsResponseDTO> {
    const offset = (page - 1) * limit;
    
    const reviews = await db.query(REVIEWS.GET_APPROVED_REVIEWS_FOR_PROFILE, [
      profileId,
      limit,
      offset,
    ]);
    
    const totalCount = await db.query(
      `SELECT COUNT(*) as total FROM agn.reviews WHERE "revieweeId" = $1 AND "statusId" = 2`,
      [profileId]
    );
    
    const total = parseInt(totalCount.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    const pagination: PaginationDTO = {
      total,
      totalPages,
      currentPage: page,
      perPage: limit,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };

    return {
      reviews: reviews.rows as ReviewDTO[],
      pagination,
    };
  }

  async getProfileRatingStats(profileId: number) {
    const { rows } = await db.query(REVIEWS.GET_PROFILE_RATING_STATS, [profileId]);
    const stats = rows[0];
    
    return {
      totalReviews: parseInt(stats.totalReviews) || 0,
      averageRating: parseFloat(stats.averageRating) || 0,
      repliedCount: parseInt(stats.repliedCount) || 0,
      flaggedCount: parseInt(stats.flaggedCount) || 0
    };
  }

  async getUserReceivedReviews(
    userId: number,
    page: number = 1,
    limit: number = 10
  ): Promise<GetAllReviewsResponseDTO> {
    const offset = (page - 1) * limit;
    
    const reviews = await db.query(REVIEWS.GET_USER_RECEIVED_REVIEWS, [
      userId,
      limit,
      offset,
    ]);
    
    const totalCount = await db.query(
      `SELECT COUNT(*) as total 
       FROM agn.reviews r
       LEFT JOIN look.status s ON r."statusId" = s.id
       WHERE r."revieweeId" = $1 
         AND r."reviewerId" != $1
         AND s.name IN ('approved', 'confirmed')`,
      [userId]
    );
    
    const total = parseInt(totalCount.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    const pagination: PaginationDTO = {
      total,
      totalPages,
      currentPage: page,
      perPage: limit,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };

    return {
      reviews: reviews.rows as ReviewDTO[],
      pagination,
    };
  }

  async   getAgentReviews(
    agentId: number,
    page: number = 1,
    limit: number = 10,
    includeAll: boolean = false,
    filter: string = 'all',
    search: string = ''
  ): Promise<GetAllReviewsResponseDTO> {
    const offset = (page - 1) * limit;
    
    // Get status IDs
    const publishedStatusId = await this.getStatusIdByName('Publish');
    
    // Build filter conditions based on the filter parameter
    let filterCondition = '';
    let countFilterCondition = '';
    let needsStatusParam = false;
    
    switch (filter.toLowerCase()) {
      case 'all':
        if (includeAll) {
          filterCondition = `AND (r."statusId" = $4 OR r."flagged" = true)`;
          countFilterCondition = `AND ("statusId" = $2 OR "flagged" = true)`;
          needsStatusParam = true;
        } else {
          filterCondition = `AND r."statusId" = $4`;
          countFilterCondition = `AND "statusId" = $2`;
          needsStatusParam = true;
        }
        break;
      case 'replied':
        if (includeAll) {
          filterCondition = `AND r."replyText" IS NOT NULL AND r."replyText" != ''`;
          countFilterCondition = `AND "replyText" IS NOT NULL AND "replyText" != ''`;
        } else {
          filterCondition = `AND r."replyText" IS NOT NULL AND r."replyText" != '' AND r."statusId" = $4`;
          countFilterCondition = `AND "replyText" IS NOT NULL AND "replyText" != '' AND "statusId" = $2`;
          needsStatusParam = true;
        }
        break;
      case 'unreplied':
        if (includeAll) {
          filterCondition = `AND (r."replyText" IS NULL OR r."replyText" = '')`;
          countFilterCondition = `AND ("replyText" IS NULL OR "replyText" = '')`;
        } else {
          filterCondition = `AND (r."replyText" IS NULL OR r."replyText" = '') AND r."statusId" = $4`;
          countFilterCondition = `AND ("replyText" IS NULL OR "replyText" = '') AND "statusId" = $2`;
          needsStatusParam = true;
        }
        break;
      case 'flagged':
        filterCondition = `AND r."flagged" = true`;
        countFilterCondition = `AND "flagged" = true`;
        break;
      case 'published':
        filterCondition = `AND r."statusId" = $4`;
        countFilterCondition = `AND "statusId" = $2`;
        needsStatusParam = true;
        break;
      case 'high_rating':
        if (includeAll) {
          filterCondition = `AND r.rating >= 4`;
          countFilterCondition = `AND rating >= 4`;
        } else {
          filterCondition = `AND r.rating >= 4 AND r."statusId" = $4`;
          countFilterCondition = `AND rating >= 4 AND "statusId" = $2`;
          needsStatusParam = true;
        }
        break;
      case 'low_rating':
        if (includeAll) {
          filterCondition = `AND r.rating <= 2`;
          countFilterCondition = `AND rating <= 2`;
        } else {
          filterCondition = `AND r.rating <= 2 AND r."statusId" = $4`;
          countFilterCondition = `AND rating <= 2 AND "statusId" = $2`;
          needsStatusParam = true;
        }
        break;
      default:
        if (includeAll) {
          filterCondition = `AND (r."statusId" = $4 OR r."flagged" = true)`;
          countFilterCondition = `AND ("statusId" = $2 OR "flagged" = true)`;
          needsStatusParam = true;
        } else {
          filterCondition = `AND r."statusId" = $4`;
          countFilterCondition = `AND "statusId" = $2`;
          needsStatusParam = true;
        }
    }
    
    // Add search condition if search term is provided
    let searchCondition = '';
    let searchCountCondition = '';
    let searchParams: any[] = [];
    let paramIndex = needsStatusParam ? 5 : 4; // Adjust based on whether status param is used
    
    if (search && search.trim().length > 0) {
      const searchTerm = `%${search.trim().toLowerCase()}%`;
      searchCondition = `AND (
        LOWER(r."reviewText") LIKE $${paramIndex} OR 
        LOWER(reviewer."firstName") LIKE $${paramIndex} OR 
        LOWER(reviewer."lastName") LIKE $${paramIndex} OR 
        LOWER(CONCAT(reviewer."firstName", ' ', reviewer."lastName")) LIKE $${paramIndex} OR
        LOWER(reviewer.email) LIKE $${paramIndex}
      )`;
      
      // For count query, parameter index is different
      const countParamIndex = needsStatusParam ? 3 : 2;
      searchCountCondition = `AND (
        LOWER("reviewText") LIKE $${countParamIndex} OR 
        EXISTS (
          SELECT 1 FROM prf.profile p 
          WHERE p.id = "reviewerId" AND (
            LOWER(p."firstName") LIKE $${countParamIndex} OR 
            LOWER(p."lastName") LIKE $${countParamIndex} OR 
            LOWER(CONCAT(p."firstName", ' ', p."lastName")) LIKE $${countParamIndex} OR
            LOWER(p.email) LIKE $${countParamIndex}
          )
        )
      )`;
      searchParams.push(searchTerm);
    }
    
    // Build the main query
    const query = `
      SELECT 
        r.id,
        r."reviewerId",
        r."revieweeId",
        r."reviewText",
        r.rating,
        r."statusId",
        r."hideReason",
        r."flagged",
        r."created_at",
        r."updated_at",
        
        -- Reply fields
        r."replyText",
        r."replyDate",
        r."repliedBy",
        
        -- Reviewer details
        reviewer."firstName" AS "reviewerFirstName",
        reviewer."lastName" AS "reviewerLastName",
        reviewer.email AS "reviewerEmail",
        reviewer."accountType" AS "reviewerType",
        
        -- Reviewee details (the agent)
        reviewee."firstName" AS "revieweeFirstName",
        reviewee."lastName" AS "revieweeLastName",
        reviewee."accountType" AS "revieweeType",
        
        -- Agency name if reviewee is agency
        CASE 
          WHEN reviewee."accountType" = 'Company/Agency/PropertyDeveloper' THEN agn.name
          ELSE NULL
        END AS "agencyName",
        
        -- Status details
        s.name AS "statusName",
        
        -- Admin notes aggregated as JSON array (for transparency)
        COALESCE(
          (SELECT JSON_AGG(
            JSON_BUILD_OBJECT(
              'id', rn.id,
              'note', rn.note,
              'createdAt', rn."created_at",
              'adminName', CONCAT(admin."firstName", ' ', admin."lastName")
            )
            ORDER BY rn."created_at" DESC
          )
          FROM agn.review_notes rn
          LEFT JOIN sec.login l ON rn."createdBy" = l.id
          LEFT JOIN prf.profile admin ON l."profileId" = admin.id
          WHERE rn."reviewId" = r.id),
          '[]'::json
        ) AS "notes"
        
      FROM agn.reviews r
      LEFT JOIN prf.profile reviewer ON r."reviewerId" = reviewer.id
      LEFT JOIN prf.profile reviewee ON r."revieweeId" = reviewee.id
      LEFT JOIN agn.agencies agn ON r."revieweeId" = agn."profileId"
      LEFT JOIN look.status s ON r."statusId" = s.id
      WHERE r."revieweeId" = $1 ${filterCondition} ${searchCondition}
      ORDER BY r."created_at" DESC
      LIMIT $2 OFFSET $3
    `;
    
    const countQuery = `SELECT COUNT(*) as total FROM agn.reviews WHERE "revieweeId" = $1 ${countFilterCondition} ${searchCountCondition}`;
    
    let queryParams: any[];
    let countParams: any[];
    
    // Set parameters based on whether status parameter is needed
    if (needsStatusParam) {
      queryParams = [agentId, limit, offset, publishedStatusId, ...searchParams];
      countParams = [agentId, publishedStatusId, ...searchParams];
    } else {
      queryParams = [agentId, limit, offset, ...searchParams];
      countParams = [agentId, ...searchParams];
    }
    
    const reviews = await db.query(query, queryParams);
    const totalCount = await db.query(countQuery, countParams);
    
    const total = parseInt(totalCount.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    const pagination: PaginationDTO = {
      total,
      totalPages,
      currentPage: page,
      perPage: limit,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };

    return {
      reviews: reviews.rows as ReviewDTO[],
      pagination,
    };
  }

  async checkExistingReview(reviewerId: number, revieweeId: number): Promise<boolean> {
    const { rows } = await db.query(REVIEWS.CHECK_EXISTING_REVIEW, [
      reviewerId,
      revieweeId,
    ]);
    return rows.length > 0;
  }

  async flagReview(id: number, flagged: boolean, reason?: string, flaggedBy?: number, additionalDetails?: string): Promise<void> {
    console.log('[flagReview] Called with:', { id, flagged, reason, flaggedBy, additionalDetails });
    
    if (flagged) {
      // When flagging, set all flag-related fields
      const result = await db.query(
        `UPDATE agn.reviews SET 
         "flagged" = $1, 
         "flaggedReason" = $2, 
         "flaggedBy" = $3, 
         "flaggedAt" = CURRENT_TIMESTAMP,
         "additionalDetails" = $4,
         "updated_at" = CURRENT_TIMESTAMP 
         WHERE id = $5`,
        [flagged, reason, flaggedBy, additionalDetails, id]
      );
      console.log('[flagReview] Flag update result:', { rowCount: result.rowCount });
      if (result.rowCount === 0) {
        throw new Error(`Review with id ${id} does not exist.`);
      }
    } else {
      // When unflagging, clear all flag-related fields
      const result = await db.query(
        `UPDATE agn.reviews SET 
         "flagged" = $1, 
         "flaggedReason" = NULL, 
         "flaggedBy" = NULL, 
         "flaggedAt" = NULL,
         "additionalDetails" = NULL,
         "updated_at" = CURRENT_TIMESTAMP 
         WHERE id = $2`,
        [flagged, id]
      );
      console.log('[flagReview] Unflag update result:', { rowCount: result.rowCount });
      if (result.rowCount === 0) {
        throw new Error(`Review with id ${id} does not exist.`);
      }
    }
  }

  async addReviewNote(reviewId: number, note: string, adminId: number): Promise<void> {
    const result = await db.query(REVIEWS.ADD_REVIEW_NOTE, [reviewId, note, adminId]);
    if (result.rowCount === 0) {
      throw new Error(`Review with id ${reviewId} does not exist.`);
    }
  }

  async getReviewNotes(reviewId: number) {
    const { rows } = await db.query(REVIEWS.GET_REVIEW_NOTES, [reviewId]);
    return rows;
  }

  async getReviewHistory(reviewId: number) {
    const { rows } = await db.query(REVIEWS.GET_REVIEW_HISTORY, [reviewId]);
    return rows;
  }

  async getPreviousStatusBeforeHidden(reviewId: number): Promise<number | null> {
    const { rows } = await db.query(REVIEWS.GET_PREVIOUS_STATUS_BEFORE_HIDDEN, [reviewId]);
    return rows.length > 0 ? rows[0].previousStatus : null;
  }

  async getLastStatusBeforeHidden(reviewId: number): Promise<number | null> {
    const { rows } = await db.query(REVIEWS.GET_LAST_STATUS_BEFORE_HIDDEN, [reviewId]);
    return rows.length > 0 ? rows[0].statusId : null;
  }

  async logReviewAction(
    reviewId: number,
    action: string,
    previousStatus: number | null,
    newStatus: number,
    notes: string | null,
    adminId: number
  ): Promise<void> {
    await db.query(REVIEWS.LOG_REVIEW_ACTION, [
      reviewId,
      action,
      previousStatus,
      newStatus,
      notes,
      adminId
    ]);
  }

  async getCurrentReviewStatus(reviewId: number): Promise<number | null> {
    const { rows } = await db.query(`SELECT "statusId" FROM agn.reviews WHERE id = $1`, [reviewId]);
    return rows.length > 0 ? rows[0].statusId : null;
  }

  async getHiddenStatusId(): Promise<number> {
    const { rows } = await db.query(STATUS.GET_HIDDEN_STATUS);
    if (rows.length > 0) {
      return rows[0].id;
    } else {
      return 31; // Hidden status ID from seeds
    }
  }

  async getStatusIdByName(statusName: string): Promise<number> {
    const { rows } = await db.query(STATUS.GET_STATUS_BY_NAME, [statusName]);
    if (rows.length > 0) {
      return rows[0].id;
    } else {
      // Fallback to correct status IDs based on seeds
      switch (statusName.toLowerCase()) {
        case 'pending':
          return 3; // Pending status ID from seeds
        case 'approved':
        case 'published':
        case 'publish':
        case 'confirmed':
          return 30; // Confirmed status ID from seeds (used as Approved for reviews)
        case 'rejected':
          return 8; // Rejected status ID from seeds
        case 'deleted':
        case 'hidden':
          return 31; // Hidden status ID from seeds
        default:
          throw new Error(`Status '${statusName}' not found`);
      }
    }
  }

  // Reply functionality methods
  async addReviewReply(reviewId: number, replyText: string, agentId: number) {
    const query = `
      UPDATE agn.reviews 
      SET "replyText" = $1, "replyDate" = CURRENT_TIMESTAMP, "repliedBy" = $2
      WHERE id = $3
      RETURNING id, "replyText", "replyDate", "repliedBy"
    `;
    
    const { rows } = await db.query(query, [replyText, agentId, reviewId]);
    return rows[0];
  }

  async updateReviewReply(reviewId: number, replyText: string, agentId: number) {
    const query = `
      UPDATE agn.reviews 
      SET "replyText" = $1, "replyDate" = CURRENT_TIMESTAMP, "repliedBy" = $2
      WHERE id = $3
      RETURNING id, "replyText", "replyDate", "repliedBy"
    `;
    
    const { rows } = await db.query(query, [replyText, agentId, reviewId]);
    if (rows.length === 0) {
      throw new Error("Review not found");
    }
    return rows[0];
  }

  async deleteReviewReply(reviewId: number) {
    const query = `
      UPDATE agn.reviews 
      SET "replyText" = NULL, "replyDate" = NULL, "repliedBy" = NULL
      WHERE id = $1
      RETURNING id
    `;
    
    const { rows } = await db.query(query, [reviewId]);
    return rows[0];
  }
}
