import { Request, Response } from "express";
import asyncHandler from "../../../middleware/trycatch";
import db from "../../../config/database";
import { error, responseData } from "../../../utils/response";
import {
  deleteFileFromS3,
  uploadFileToS3,
} from "../../../utils/services/s3-bucket";
import {
  createStripeCoupon,
  deactivateStripeCoupon,
} from "../../../utils/helperFunctions/coupons";

// -------------------- GET SETTINGS --------------------
export const getSettings = asyncHandler(async (req: Request, res: Response) => {
  const { rows } = await db.query(
    `SELECT * FROM web.settings ORDER BY id ASC LIMIT 1`
  );
  if (rows.length === 0) return error(res, 404, "Settings not found.");
  return responseData(res, 200, "Settings retrieved successfully.", rows[0]);
});

// -------------------- UPSERT SETTINGS --------------------
export const upsertSettings = asyncHandler(
  async (req: Request, res: Response) => {
    const {
      app_name,
      copyright_text,
      monthly_discount_type,
      monthly_discount_value,
      monthly_coupon_code,
      yearly_discount_type,
      yearly_discount_value,
      yearly_coupon_code,
    } = req.body;

    const required = [
      "app_name",
      "monthly_discount_type",
      "monthly_discount_value",
      "yearly_discount_type",
      "yearly_discount_value",
    ];

    for (const field of required) {
      if (
        req.body[field] === undefined ||
        req.body[field] === null ||
        req.body[field] === ""
      ) {
        return error(res, 400, `Missing required field: ${field}`);
      }
    }

    const files = req.files as { [fieldname: string]: Express.Multer.File[] };
    const uploadedKeys: string[] = [];

    let logoS3Url: string | null = req.body.logo ?? null;
    let faviconS3Url: string | null = req.body.favicon ?? null;

    const client = await db.connect();
    try {
      await client.query("BEGIN");

      const toUpload: Promise<any>[] = [];

      if (files?.logo?.[0]) {
        toUpload.push(
          uploadFileToS3(
            files.logo[0].filename,
            files.logo[0],
            "admin",
            "logo"
          ).then((result) => {
            logoS3Url = result.fileKey;
            uploadedKeys.push(result.fileKey);
          })
        );
      }

      if (files?.favicon?.[0]) {
        toUpload.push(
          uploadFileToS3(
            files.favicon[0].filename,
            files.favicon[0],
            "admin",
            "favicon"
          ).then((result) => {
            faviconS3Url = result.fileKey;
            uploadedKeys.push(result.fileKey);
          })
        );
      }

      await Promise.all(toUpload);

      const { rows: existingRows } = await client.query(
        `SELECT * FROM web.settings ORDER BY id ASC LIMIT 1`
      );
      const existing = existingRows?.[0];

      let monthlyCouponId = existing?.monthly_coupon_id ?? null;
      let yearlyCouponId = existing?.yearly_coupon_id ?? null;

      const monthlyChanged =
        !existing ||
        existing.monthly_discount_type !== monthly_discount_type ||
        Number(existing.monthly_discount_value) !==
          Number(monthly_discount_value);

      if (monthlyChanged && existing?.monthly_coupon_id) {
        await deactivateStripeCoupon(existing.monthly_coupon_id);
        monthlyCouponId = null;
      }

      if (monthlyChanged && monthly_coupon_code) {
        const newMonthly = await createStripeCoupon({
          id: `monthly-${Date.now()}`,
          name: `Monthly Discount - ${monthly_discount_type} ${monthly_discount_value}`,
          currency: "aed",
          percent_off:
            monthly_discount_type === "percentage"
              ? Number(monthly_discount_value)
              : undefined,
          amount_off:
            monthly_discount_type === "fixed"
              ? Math.round(Number(monthly_discount_value) * 100)
              : undefined,
        });

        if (!newMonthly.success) throw new Error(newMonthly.error);
        monthlyCouponId = newMonthly.data?.id;
      }

      const yearlyChanged =
        !existing ||
        existing.yearly_discount_type !== yearly_discount_type ||
        Number(existing.yearly_discount_value) !==
          Number(yearly_discount_value);

      if (yearlyChanged && existing?.yearly_coupon_id) {
        await deactivateStripeCoupon(existing.yearly_coupon_id);
        yearlyCouponId = null;
      }

      if (yearlyChanged && yearly_coupon_code) {
        const newYearly = await createStripeCoupon({
          id: `yearly-${Date.now()}`,
          name: `Yearly Discount - ${yearly_discount_type} ${yearly_discount_value}`,
          currency: "aed",
          percent_off:
            yearly_discount_type === "percentage"
              ? Number(yearly_discount_value)
              : undefined,
          amount_off:
            yearly_discount_type === "fixed"
              ? Math.round(Number(yearly_discount_value) * 100)
              : undefined,
        });

        if (!newYearly.success) throw new Error(newYearly.error);
        yearlyCouponId = newYearly.data?.id;
      }

      // Coerce IDs to string
      monthlyCouponId = monthlyCouponId ? String(monthlyCouponId) : null;
      yearlyCouponId = yearlyCouponId ? String(yearlyCouponId) : null;

      if (existing) {
        // Perform UPDATE
        await client.query(
          `UPDATE web.settings SET
            app_name = $1,
            logo = $2,
            favicon = $3,
            copyright_text = $4,
            monthly_discount_type = $5,
            monthly_discount_value = $6,
            monthly_coupon_code = $7,
            monthly_coupon_id = $8,
            yearly_discount_type = $9,
            yearly_discount_value = $10,
            yearly_coupon_code = $11,
            yearly_coupon_id = $12,
            updated_at = NOW()
          WHERE id = $13`,
          [
            app_name,
            logoS3Url,
            faviconS3Url,
            copyright_text,
            monthly_discount_type,
            monthly_discount_value,
            monthly_coupon_code,
            monthlyCouponId,
            yearly_discount_type,
            yearly_discount_value,
            yearly_coupon_code,
            yearlyCouponId,
            existing.id,
          ]
        );
      } else {
        // Perform INSERT
        await client.query(
          `INSERT INTO web.settings (
            app_name, logo, favicon, copyright_text,
            monthly_discount_type, monthly_discount_value, monthly_coupon_code, monthly_coupon_id,
            yearly_discount_type, yearly_discount_value, yearly_coupon_code, yearly_coupon_id,
            created_at, updated_at
          ) VALUES (
            $1, $2, $3, $4,
            $5, $6, $7, $8,
            $9, $10, $11, $12,
            NOW(), NOW()
          )`,
          [
            app_name,
            logoS3Url,
            faviconS3Url,
            copyright_text,
            monthly_discount_type,
            monthly_discount_value,
            monthly_coupon_code,
            monthlyCouponId,
            yearly_discount_type,
            yearly_discount_value,
            yearly_coupon_code,
            yearlyCouponId,
          ]
        );
      }

      const { rows: updatedRows } = await client.query(
        `SELECT * FROM web.settings ORDER BY id ASC LIMIT 1`
      );

      await client.query("COMMIT");
      return responseData(
        res,
        200,
        "Settings saved successfully.",
        updatedRows[0]
      );
    } catch (err) {
      await client.query("ROLLBACK");
      console.error("Upsert settings failed:", err);

      for (const key of uploadedKeys) {
        await deleteFileFromS3(key);
      }

      return error(res, 500, "Failed to save settings.");
    } finally {
      client.release();
    }
  }
);
