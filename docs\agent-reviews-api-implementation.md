# Agent Review APIs - Implementation Documentation

## Overview

This document outlines the comprehensive agent review APIs that have been implemented to allow agents to manage reviews from their perspective. These APIs mirror the functionality of the admin review APIs but are specifically designed for agent-level access with appropriate authentication and authorization.

## Authentication & Authorization

All agent review APIs require:
- **Authentication**: `authMiddleware` - Agents must be logged in with a valid `authToken` cookie
- **Authorization**: Agents can only access reviews that belong to them (either as reviewee or reviewer)
- **Account Status**: Agent account must be activated (`isActivated: true`)

## API Endpoints

### Base URL: `/api/agents/reviews`

### 1. Get Agent's Own Reviews (All Statuses)
```http
GET /api/agents/reviews/my-reviews
```

**Description**: Retrieves all reviews received by the logged-in agent, including pending, approved, and rejected reviews.

**Query Parameters**:
- `page` (optional): Page number for pagination (default: 1)
- `limit` (optional): Number of reviews per page (default: 10)

**Response**:
```json
{
  "status": 200,
  "message": "Reviews fetched successfully",
  "data": {
    "reviews": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

### 2. Get Agent's Public Reviews (Approved Only)
```http
GET /api/agents/reviews/my-public-reviews
```

**Description**: Retrieves only approved reviews for the agent (public view).

**Query Parameters**: Same as above

### 3. Get Reviews Given by Agent
```http
GET /api/agents/reviews/given-reviews
```

**Description**: Retrieves reviews that the agent has written about other agents/agencies.

**Query Parameters**: Same as above

### 4. Get Agent's Review Statistics
```http
GET /api/agents/reviews/stats
```

**Description**: Retrieves review statistics and rating analytics for the agent.

**Response**:
```json
{
  "status": 200,
  "message": "Review statistics fetched successfully",
  "data": {
    "averageRating": 4.2,
    "totalReviews": 25,
    "ratingDistribution": {
      "5": 12,
      "4": 8,
      "3": 3,
      "2": 1,
      "1": 1
    }
  }
}
```

### 5. Get Specific Review Details
```http
GET /api/agents/reviews/:id
```

**Description**: Retrieves details of a specific review (only if it belongs to the agent).

**Path Parameters**:
- `id`: Review ID

**Authorization**: Agent can only access reviews where they are the reviewee

### 6. Create New Review
```http
POST /api/agents/reviews
```

**Description**: Allows an agent to create a review for another agent/agency.

**Request Body**:
```json
{
  "revieweeId": 123,
  "reviewText": "Great service and very professional",
  "rating": 5
}
```

**Validation**:
- `revieweeId`: Required, must be a valid agent/agency
- `reviewText`: Required, cannot be empty
- `rating`: Required, must be between 1 and 5
- Agent cannot review themselves
- Agent cannot review the same profile twice

### 7. Get Review Notes
```http
GET /api/agents/reviews/:id/notes
```

**Description**: Retrieves admin notes for a specific review (only if review belongs to the agent).

**Authorization**: Agent can only access notes for their own reviews

### 8. Get Review History
```http
GET /api/agents/reviews/:id/history
```

**Description**: Retrieves status change history for a specific review.

**Authorization**: Agent can only access history for their own reviews

### 9. Flag Review
```http
PATCH /api/agents/reviews/:id/flag
```

**Description**: Allows an agent to flag a review about themselves for admin attention.

**Request Body**:
```json
{
  "flagged": true,
  "reason": "Contains inappropriate content"
}
```

**Authorization**: Agent can only flag reviews where they are the reviewee

## Error Handling

### Common Error Responses

**401 Unauthorized**:
```json
{
  "status": 401,
  "message": "Unauthorized: Agent not logged in."
}
```

**403 Forbidden**:
```json
{
  "status": 403,
  "message": "You can only access your own reviews"
}
```

**404 Not Found**:
```json
{
  "status": 404,
  "message": "Review not found"
}
```

**400 Bad Request**:
```json
{
  "status": 400,
  "message": "Missing required fields: revieweeId, reviewText, and rating"
}
```

**500 Internal Server Error**:
```json
{
  "status": 500,
  "message": "Failed to fetch reviews"
}
```

## Key Differences from Admin APIs

| Feature | Admin APIs | Agent APIs |
|---------|------------|------------|
| **Authentication** | `adminAuthMiddleware` | `authMiddleware` |
| **Authorization** | Full access to all reviews | Only own reviews |
| **Status Updates** | Can approve/reject reviews | Cannot change status |
| **Review Deletion** | Can delete any review | Cannot delete reviews |
| **Review Hiding** | Can hide/restore reviews | Cannot hide reviews |
| **Flagging** | Can flag any review | Can only flag own reviews |
| **Notes** | Can add admin notes | Can only view notes |
| **Review Creation** | Cannot create reviews | Can create reviews |

## Security Considerations

1. **Data Isolation**: Agents can only access reviews related to their profile
2. **Input Validation**: All inputs are validated before processing
3. **Rate Limiting**: Consider implementing rate limiting for review creation
4. **Audit Trail**: All actions are logged in the review history
5. **Token Validation**: JWT tokens are validated on every request

## Usage Examples

### Creating a Review
```javascript
// POST /api/agents/reviews
const response = await fetch('/api/agents/reviews', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    revieweeId: 456,
    reviewText: 'Excellent agent, very responsive and knowledgeable',
    rating: 5
  })
});
```

### Getting Own Reviews
```javascript
// GET /api/agents/reviews/my-reviews?page=1&limit=10
const response = await fetch('/api/agents/reviews/my-reviews?page=1&limit=10');
const data = await response.json();
```

### Flagging a Review
```javascript
// PATCH /api/agents/reviews/123/flag
const response = await fetch('/api/agents/reviews/123/flag', {
  method: 'PATCH',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    flagged: true,
    reason: 'Contains false information'
  })
});
```

## Testing

### Test Cases to Cover

1. **Authentication Tests**:
   - Access without token
   - Access with invalid token
   - Access with expired token

2. **Authorization Tests**:
   - Agent accessing another agent's reviews
   - Agent flagging reviews not about themselves
   - Agent creating duplicate reviews

3. **Functionality Tests**:
   - CRUD operations
   - Pagination
   - Input validation
   - Error handling

4. **Edge Cases**:
   - Empty review text
   - Invalid rating values
   - Non-existent reviewee
   - Self-review attempts

## Integration Notes

The agent review APIs are integrated into the existing application structure:

- **Routes**: `routes/agents/ReviewRoutes.ts` (included in `routes/agents/index.ts`)
- **Controller**: `controller/agents/ReviewController.ts`
- **Service**: Uses existing `service/ReviewsService.ts`
- **Repository**: Uses existing `repo/ReviewsRepository.ts`
- **Middleware**: Uses existing `middleware/authMiddleware.ts`

This ensures consistency with the existing codebase and leverages the established patterns and infrastructure.
