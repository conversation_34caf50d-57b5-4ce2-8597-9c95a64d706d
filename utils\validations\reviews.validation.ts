import { z } from "zod";

// Schema for creating a review
export const createReviewSchema = z.object({
  revieweeId: z
    .number({
      required_error: "Reviewee ID is required",
    })
    .int("Reviewee ID must be an integer")
    .positive("Reviewee ID must be a positive integer"),
  
  reviewText: z
    .string({
      required_error: "Review text is required",
    })
    .min(10, "Review text must be at least 10 characters long")
    .max(1000, "Review text cannot exceed 1000 characters")
    .trim(),
  
  rating: z
    .number({
      required_error: "Rating is required",
    })
    .int("Rating must be an integer")
    .min(1, "Rating must be at least 1")
    .max(5, "Rating cannot be more than 5"),
});

// Schema for query parameters when getting reviews
export const getReviewsQuerySchema = z.object({
  page: z
    .string()
    .optional()
    .transform((val) => val ? parseInt(val, 10) : 1)
    .pipe(z.number().int().min(1, "Page must be a positive integer")),
  
  limit: z
    .string()
    .optional()
    .transform((val) => val ? parseInt(val, 10) : 10)
    .pipe(z.number().int().min(1, "Limit must be at least 1").max(100, "Limit cannot exceed 100")),
  
  search: z.string().optional(),
  status: z.string().optional(),
});

// Schema for updating review status (admin)
export const updateReviewStatusSchema = z.object({
  status: z
    .string({
      required_error: "Status is required",
    })
    .refine(
      (val) => ["approved", "approve", "published", "publish", "hidden", "rejected", "reject", "pending", "deleted", "Approved", "Approve", "Published", "Publish", "Hidden", "Rejected", "Reject", "Pending", "Deleted"].includes(val),
      "Status must be one of: approved, published, hidden, rejected, pending, deleted"
    ),
  
  note: z
    .string()
    .max(500, "Note cannot exceed 500 characters")
    .optional(),
});

// Schema for flagging a review
export const flagReviewSchema = z.object({
  flagged: z
    .boolean()
    .optional()
    .default(true),
});

// Schema for bulk updating review status
export const bulkUpdateReviewsStatusSchema = z.object({
  reviewIds: z
    .array(z.number().int().positive("Review ID must be a positive integer"))
    .min(1, "At least one review ID is required"),
  
  status: z
    .string({
      required_error: "Status is required",
    })
    .refine(
      (val) => ["Approved", "Hidden", "Rejected", "Pending"].includes(val),
      "Status must be one of: Approved, Hidden, Rejected, Pending"
    ),
  
  note: z
    .string()
    .max(500, "Note cannot exceed 500 characters")
    .optional(),
});

// Schema for adding review notes
export const addReviewNoteSchema = z.object({
  note: z
    .string({
      required_error: "Note is required",
    })
    .min(1, "Note cannot be empty")
    .max(500, "Note cannot exceed 500 characters")
    .trim(),
});

// Schema for hiding a review
export const hideReviewSchema = z.object({
  reason: z
    .string({
      required_error: "Reason is required for hiding a review",
    })
    .min(5, "Reason must be at least 5 characters long")
    .max(200, "Reason cannot exceed 200 characters")
    .trim(),
});
