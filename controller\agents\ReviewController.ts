import { Request, Response } from "express";
import { ReviewService } from "../../service/ReviewsService";
import asyncHandler from "../../middleware/trycatch";
import { response, responseData } from "../../utils/response";

interface AuthenticatedRequest extends Request {
  user?: {
    id: number;
    [key: string]: any;
  };
}

export class ReviewController {
  private reviewService = new ReviewService();

  getAgentReviews = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const agentId = req.user?.id;
    if (!agentId) {
      return response(res, 401, "Unauthorized: Agent not logged in.");
    }
    
    const page = req.query.page ? parseInt(req.query.page as string) : 1;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
    const filter = req.query.filter as string || 'all';
    const search = req.query.search as string || '';
    
    const result = await this.reviewService.getAgentReviews(agentId, page, limit, true, filter, search);
    return responseData(res, 200, "Reviews fetched successfully", result);
  });

  getReviewById = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const agentId = req.user?.id;
    if (!agentId) {
      return response(res, 401, "Unauthorized: Agent not logged in.");
    }

    const reviewId = Number(req.params.id);
    const review = await this.reviewService.getReviewById(reviewId);
    
    if (review.revieweeId !== agentId) {
      return response(res, 403, "You can only access your own reviews");
    }
    
    return responseData(res, 200, "Review fetched successfully", review);
  });

  getMyReviewStats = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const agentId = req.user?.id;
    if (!agentId) {
      return response(res, 401, "Unauthorized: Agent not logged in.");
    }

    const stats = await this.reviewService.getProfileRatingStats(agentId);
    return responseData(res, 200, "Review statistics fetched successfully", stats);
  });

  createReview = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const reviewerId = req.user?.id;
    if (!reviewerId) {
      return response(res, 401, "Unauthorized: Agent not logged in.");
    }

    const { revieweeId, reviewText, rating } = req.body;
    
    if (!revieweeId || !reviewText || !rating) {
      return response(res, 400, "Missing required fields: revieweeId, reviewText, and rating");
    }

    const result = await this.reviewService.createReview(reviewerId, revieweeId, reviewText, rating);
    return responseData(res, 201, "Review created successfully", result);
  });

  flagReview = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const agentId = req.user?.id;
    if (!agentId) {
      return response(res, 401, "Unauthorized: Agent not logged in.");
    }

    const reviewId = Number(req.params.id);
    const { flagged = true, reason } = req.body;
    
    // Validate review ID
    if (!reviewId || isNaN(reviewId)) {
      return response(res, 400, "Invalid review ID provided");
    }

    // Validate reason when flagging
    if (flagged && (!reason || reason.trim().length === 0)) {
      return response(res, 400, "Reason is required when flagging a review");
    }

    if (reason && reason.trim().length > 500) {
      return response(res, 400, "Reason cannot exceed 500 characters");
    }

    try {
      const review = await this.reviewService.getReviewById(reviewId);
      if (review.revieweeId !== agentId) {
        return response(res, 403, "You can only flag reviews about yourself");
      }

      // Use the enhanced flagReview method with reason stored in database
      await this.reviewService.flagReview(reviewId, flagged, reason?.trim(), agentId);
      
      const message = flagged 
        ? "Review flagged successfully and will be reviewed by administrators" 
        : "Review unflagged successfully";
      
      return response(res, 200, message);
    } catch (error: any) {
      if (error.message.includes("not found") || error.message.includes("does not exist")) {
        return response(res, 404, "Review not found");
      }
      console.error("Error flagging review:", error);
      return response(res, 500, "Failed to flag review");
    }
  });

  replyToReview = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const agentId = req.user?.id;
    if (!agentId) {
      return response(res, 401, "Unauthorized: Agent not logged in.");
    }

    const reviewId = Number(req.params.id);
    const { replyText } = req.body;
    
    if (!replyText || replyText.trim().length === 0) {
      return response(res, 400, "Reply text is required");
    }

    try {
      const result = await this.reviewService.addReviewReply(reviewId, replyText, agentId);
      return responseData(res, 200, "Reply added successfully", result);
    } catch (error: any) {
      if (error.message.includes("not found") || error.message.includes("does not exist")) {
        return response(res, 404, error.message);
      }
      if (error.message.includes("can only reply") || error.message.includes("already has a reply")) {
        return response(res, 403, error.message);
      }
      console.error("Error adding reply:", error);
      return response(res, 500, "Failed to add reply");
    }
  });

  updateReviewReply = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const agentId = req.user?.id;
    if (!agentId) {
      return response(res, 401, "Unauthorized: Agent not logged in.");
    }

    const reviewId = Number(req.params.id);
    const { replyText } = req.body;
    
    if (!replyText || replyText.trim().length === 0) {
      return response(res, 400, "Reply text is required");
    }

    try {
      const result = await this.reviewService.updateReviewReply(reviewId, replyText, agentId);
      return responseData(res, 200, "Reply updated successfully", result);
    } catch (error: any) {
      if (error.message.includes("not found") || error.message.includes("does not exist")) {
        return response(res, 404, error.message);
      }
      if (error.message.includes("can only update") || error.message.includes("No reply found")) {
        return response(res, 403, error.message);
      }
      console.error("Error updating reply:", error);
      return response(res, 500, "Failed to update reply");
    }
  });

  deleteReviewReply = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const agentId = req.user?.id;
    if (!agentId) {
      return response(res, 401, "Unauthorized: Agent not logged in.");
    }

    const reviewId = Number(req.params.id);
    
    try {
      await this.reviewService.deleteReviewReply(reviewId, agentId);
      return response(res, 200, "Reply deleted successfully");
    } catch (error: any) {
      if (error.message.includes("not found") || error.message.includes("does not exist")) {
        return response(res, 404, error.message);
      }
      if (error.message.includes("can only delete") || error.message.includes("No reply found")) {
        return response(res, 403, error.message);
      }
      console.error("Error deleting reply:", error);
      return response(res, 500, "Failed to delete reply");
    }
  });

  getFlagReasons = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const agentId = req.user?.id;
    if (!agentId) {
      return response(res, 401, "Unauthorized: Agent not logged in.");
    }

    const flagReasons = [
      {
        id: 1,
        reason: "Inappropriate language or content",
        description: "Review contains offensive, abusive, or inappropriate language"
      },
      {
        id: 2,
        reason: "False or misleading information",
        description: "Review contains false claims or misleading information about services"
      },
      {
        id: 3,
        reason: "Spam or fake review",
        description: "Review appears to be spam or posted by a fake account"
      },
      {
        id: 4,
        reason: "Violation of review guidelines",
        description: "Review violates platform's review posting guidelines"
      },
      {
        id: 5,
        reason: "Personal attack or harassment",
        description: "Review contains personal attacks or harassment against the agent"
      },
      {
        id: 6,
        reason: "Irrelevant content",
        description: "Review content is not related to the actual service provided"
      },
      {
        id: 7,
        reason: "Other",
        description: "Other reason not listed above (please provide details)"
      }
    ];

    return responseData(res, 200, "Flag reasons fetched successfully", flagReasons);
  });

  unflagReview = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const agentId = req.user?.id;
    if (!agentId) {
      return response(res, 401, "Unauthorized: Agent not logged in.");
    }

    const reviewId = Number(req.params.id);
    const { reason } = req.body;
    
    // Validate review ID
    if (!reviewId || isNaN(reviewId)) {
      return response(res, 400, "Invalid review ID provided");
    }

    try {
      const review = await this.reviewService.getReviewById(reviewId);
      if (review.revieweeId !== agentId) {
        return response(res, 403, "You can only unflag reviews about yourself");
      }

      // Check if review is actually flagged
      if (!review.flagged) {
        return response(res, 400, "This review is not currently flagged");
      }

      // Unflag the review (this will clear all flag-related fields in database)
      await this.reviewService.flagReview(reviewId, false);
      
      // Add note about unflagging if reason provided
      if (reason && reason.trim().length > 0) {
        const unflagNote = `Agent removed flag from review - Reason: ${reason.trim()}`;
        await this.reviewService.addReviewNote(reviewId, unflagNote, agentId);
      } else {
        await this.reviewService.addReviewNote(reviewId, "Agent removed flag from review", agentId);
      }
      
      return response(res, 200, "Review unflagged successfully");
    } catch (error: any) {
      if (error.message.includes("not found") || error.message.includes("does not exist")) {
        return response(res, 404, "Review not found");
      }
      console.error("Error unflagging review:", error);
      return response(res, 500, "Failed to unflag review");
    }
  });
}
