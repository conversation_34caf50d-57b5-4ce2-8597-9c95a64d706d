export interface ReviewNoteDTO {
  id: number;
  note: string;
  createdAt: string;
  adminName: string;
}

export interface ReviewHistoryDTO {
  id: number;
  action: string;
  previousStatus: number | null;
  newStatus: number;
  previousStatusName: string | null;
  newStatusName: string;
  notes: string | null;
  createdAt: string;
  adminName: string;
}

export interface ReviewDTO {
  id: number;
  reviewerId: number;
  revieweeId: number;
  reviewText: string;
  rating: number;
  statusId: number;
  statusName: string;
  createdAt: string;
  updatedAt: string;
  reviewerName: string;
  revieweeName: string;
  reviewerEmail?: string;
  revieweeEmail?: string;
  agencyName?: string;
  flagged?: boolean;
  notes?: ReviewNoteDTO[];
  history?: ReviewHistoryDTO[];
  // Reply fields
  replyText?: string | null;
  replyDate?: string | null;
  repliedBy?: number | null;
  // Flag fields
  flaggedReason?: string | null;
  flaggedBy?: number | null;
  flaggedAt?: string | null;
  additionalDetails?: string | null;
}
