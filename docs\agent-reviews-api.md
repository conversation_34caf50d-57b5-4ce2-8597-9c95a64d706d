# Agent Reviews API

## Get Agent Reviews Endpoint

### Endpoint
`GET /api/reviews/agent/:agentId`

### Description
Retrieves all reviews for a specific agent. The response varies based on authentication:
- **Public users**: Only approved/published reviews are returned
- **Logged-in agent (viewing their own reviews)**: All reviews are returned (including pending, rejected, etc.)

### Parameters
- `agentId` (path parameter, required): The ID of the agent whose reviews you want to retrieve
- `page` (query parameter, optional): Page number for pagination (default: 1)
- `limit` (query parameter, optional): Number of reviews per page (default: 10)

### Authentication
- No authentication required for public access (returns only approved reviews)
- Optional authentication for agents to view their own reviews with all statuses

### Response Format
```json
{
  "success": true,
  "message": "Agent reviews retrieved successfully",
  "data": {
    "reviews": [
      {
        "id": 1,
        "reviewText": "Great agent! Very professional and helpful.",
        "rating": 5,
        "created_at": "2025-08-14T10:30:00Z",
        "updated_at": "2025-08-14T10:30:00Z",
        "reviewerFirstName": "<PERSON>",
        "reviewerLastName": "Doe",
        "reviewerEmail": "<EMAIL>",
        "reviewerType": "Individual",
        "revieweeFirstName": "Jane",
        "revieweeLastName": "Smith", 
        "revieweeType": "Individual",
        "agencyName": null,
        "statusName": "approved",
        "notes": [] // Admin notes (only visible when includeAll is true)
      }
    ],
    "pagination": {
      "total": 25,
      "totalPages": 3,
      "currentPage": 1,
      "perPage": 10,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### Error Responses

#### 400 Bad Request
```json
{
  "success": false,
  "message": "Valid agent ID is required"
}
```

```json
{
  "success": false,
  "message": "Invalid agent - only individual agents and agencies can receive reviews"
}
```

#### 404 Not Found
```json
{
  "success": false,
  "message": "Agent not found"
}
```

#### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Failed to fetch agent reviews"
}
```

### Usage Examples

#### Public Access (Approved Reviews Only)
```bash
curl -X GET "http://localhost:3000/api/reviews/agent/123?page=1&limit=5"
```

#### Authenticated Agent Viewing Own Reviews (All Statuses)
```bash
curl -X GET "http://localhost:3000/api/reviews/agent/123?page=1&limit=5" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Business Logic
1. Validates that the agent ID is a valid number
2. Checks if the agent exists in the database
3. Verifies that the agent is eligible to receive reviews (Individual or Company/Agency/PropertyDeveloper account types)
4. If the requesting user is authenticated and is the same as the agent being queried, returns all reviews
5. Otherwise, returns only approved reviews (statusId = 30)
6. Includes pagination for better performance
7. Returns detailed reviewer and reviewee information
8. For agencies, includes the agency name

### Database Tables Used
- `agn.reviews` - Main reviews table
- `prf.profile` - User profiles (reviewer and reviewee)
- `agn.agencies` - Agency information
- `look.status` - Review status information
- `agn.review_notes` - Admin notes for reviews (when includeAll is true)

### Differences from Existing `/profile/:profileId` Endpoint
- Uses agent-specific terminology and parameter naming
- Includes additional fields like admin notes when viewing own reviews
- Has built-in logic to show all reviews vs approved only based on authentication
- More comprehensive error handling for agent-specific scenarios
