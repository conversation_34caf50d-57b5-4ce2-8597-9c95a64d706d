# Flag Review APIs Documentation

This document describes the APIs available for agents to flag reviews with detailed reasons.

## Overview

The flag review system allows agents to flag reviews about themselves for administrative review. The system provides both basic flagging functionality and an improved version with predefined reasons.

## Available Endpoints

### 1. Get Flag Reasons
```
GET /agents/reviews/flag-reasons
```

**Description:** Returns a list of predefined flag reasons that agents can use when flagging reviews.

**Headers:**
- Authorization: Bearer {token}

**Response:**
```json
{
  "status": 200,
  "message": "Flag reasons fetched successfully",
  "data": [
    {
      "id": 1,
      "reason": "Inappropriate language or content",
      "description": "Review contains offensive, abusive, or inappropriate language"
    },
    {
      "id": 2,
      "reason": "False or misleading information",
      "description": "Review contains false claims or misleading information about services"
    },
    {
      "id": 3,
      "reason": "Spam or fake review",
      "description": "Review appears to be spam or posted by a fake account"
    },
    {
      "id": 4,
      "reason": "Violation of review guidelines",
      "description": "Review violates platform's review posting guidelines"
    },
    {
      "id": 5,
      "reason": "Personal attack or harassment",
      "description": "Review contains personal attacks or harassment against the agent"
    },
    {
      "id": 6,
      "reason": "Irrelevant content",
      "description": "Review content is not related to the actual service provided"
    },
    {
      "id": 7,
      "reason": "Other",
      "description": "Other reason not listed above (please provide details)"
    }
  ]
}
```

### 2. Flag Review with Reason (Recommended)
```
POST /agents/reviews/{reviewId}/flag-with-reason
```

**Description:** Flag a review using predefined reasons with optional additional details.

**Headers:**
- Authorization: Bearer {token}
- Content-Type: application/json

**Request Body:**
```json
{
  "reasonId": 1,
  "customReason": "Custom reason text (required only when reasonId is 7)",
  "additionalDetails": "Optional additional details about the flag"
}
```

**Parameters:**
- `reasonId` (required): Integer from 1-7 corresponding to predefined reasons
- `customReason` (required if reasonId = 7): String, max 200 characters
- `additionalDetails` (optional): String, max 300 characters

**Response:**
```json
{
  "status": 200,
  "message": "Review flagged successfully and will be reviewed by administrators"
}
```

### 3. Basic Flag Review (Legacy)
```
PATCH /agents/reviews/{reviewId}/flag
```

**Description:** Basic flag/unflag functionality with free-text reason.

**Headers:**
- Authorization: Bearer {token}
- Content-Type: application/json

**Request Body:**
```json
{
  "flagged": true,
  "reason": "Reason for flagging the review"
}
```

**Parameters:**
- `flagged` (optional): Boolean, defaults to true
- `reason` (required when flagging): String, max 500 characters

**Response:**
```json
{
  "status": 200,
  "message": "Review flagged successfully and will be reviewed by administrators"
}
```

### 4. Unflag Review
```
POST /agents/reviews/{reviewId}/unflag
```

**Description:** Remove a flag from a previously flagged review.

**Headers:**
- Authorization: Bearer {token}
- Content-Type: application/json

**Request Body:**
```json
{
  "reason": "Optional reason for removing the flag"
}
```

**Parameters:**
- `reason` (optional): String, max 300 characters

**Response:**
```json
{
  "status": 200,
  "message": "Review unflagged successfully"
}
```

## Authorization & Permissions

All endpoints require:
1. Valid authentication token
2. Agent can only flag reviews about themselves (revieweeId must match logged-in agent's ID)

## Error Responses

### Common Error Codes:
- `400`: Bad Request - Invalid input data
- `401`: Unauthorized - No valid authentication token
- `403`: Forbidden - Cannot flag reviews of other agents
- `404`: Not Found - Review doesn't exist
- `500`: Internal Server Error

### Example Error Response:
```json
{
  "status": 400,
  "message": "Custom reason is required when selecting 'Other'"
}
```

## Validation Rules

### Flag with Reason API:
- `reasonId`: Must be integer between 1-7
- `customReason`: Required when reasonId = 7, max 200 characters
- `additionalDetails`: Optional, max 300 characters
- Cannot flag already flagged reviews

### Basic Flag API:
- `reason`: Required when flagging, max 500 characters
- `flagged`: Must be boolean if provided

### Unflag API:
- `reason`: Optional, max 300 characters
- Can only unflag already flagged reviews

## Usage Examples

### Example 1: Flag review for inappropriate content
```bash
curl -X POST /agents/reviews/123/flag-with-reason \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "reasonId": 1,
    "additionalDetails": "Contains offensive language in the first paragraph"
  }'
```

### Example 2: Flag review with custom reason
```bash
curl -X POST /agents/reviews/123/flag-with-reason \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "reasonId": 7,
    "customReason": "Review violates privacy by sharing personal information",
    "additionalDetails": "Mentions my personal phone number and address"
  }'
```

### Example 3: Unflag a review
```bash
curl -X POST /agents/reviews/123/unflag \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "reason": "Issue has been resolved with the reviewer"
  }'
```

## Database Changes

The system uses the existing `flagged` column in the reviews table and stores detailed flag information in the notes table with structured format:

```
Agent flagged review - Reason: {selected_reason} | Additional details: {additional_details}
```

## Best Practices

1. **Use the improved API**: Prefer `/flag-with-reason` over the basic `/flag` endpoint
2. **Provide context**: Use additionalDetails to provide specific information about the issue
3. **Choose appropriate reasons**: Select the most specific reason that applies
4. **Document unflagging**: Provide a reason when unflagging to maintain audit trail

## Notes

- All flag actions are logged in the review notes for administrative tracking
- Flagged reviews are hidden from public view until reviewed by administrators
- Agents receive notifications when their flags are reviewed (if notification system is implemented)
- Flag reasons are predefined and cannot be modified through the API
