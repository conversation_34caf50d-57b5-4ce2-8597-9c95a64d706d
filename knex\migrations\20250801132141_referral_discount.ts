import type { Knex } from "knex";


export async function up(knex: Knex): Promise<void> {
      return knex.schema.createTable("referralRecords", function (table) {
    table.increments("id").primary(); // Primary key
    table.bigInteger("userId").notNullable(); // User ID  
    table.bigInteger("salesPersonId").notNullable(); // Salesperson ID
    table.string("code").nullable(); // Unique discount code
    table.timestamps(true, true);
  });
}


export async function down(knex: Knex): Promise<void> {
}

