import type { Knex } from "knex";


export async function up(knex: Knex): Promise<void> {
      await knex.schema.alterTable("commission", (table) => {
    table.string("paymentMethod").nullable();
    table.timestamp("paymentAt").nullable();
  });
}


export async function down(knex: Knex): Promise<void> {
        await knex.schema.alterTable("commission", (table) => {
        table.dropColumn("paymentMethod");
        table.dropColumn("paymentAt");
    });
}

