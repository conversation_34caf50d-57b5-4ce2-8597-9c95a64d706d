import express from "express";
import { authMiddleware } from "../../middleware/authMiddleware";
import { ReviewController } from "../../controller/reviews/ReviewController";
import { storageData } from "../../utils/services/multer";

const reviewController = new ReviewController();
const router = express.Router();
const upload = storageData("reviews");


router.use(authMiddleware);

// router.post("/", upload.none(), reviewController.createReview);
router.post("/create", upload.none(), reviewController.createReview);


export default router;
