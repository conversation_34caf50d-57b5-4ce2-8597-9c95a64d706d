import { Request, Response } from "express";
import asyncHandler from "../../../middleware/trycatch";
import db from "../../../config/database";
import { error, response, responseData } from "../../../utils/response";
import { AUTH } from "../../../utils/database/queries/auth";
import { TABLE } from "../../../utils/database/table";

// -------------------- GET ALL SALESPERSONS --------------------
export const getAllSalespersons = asyncHandler(
  async (req: Request, res: Response) => {
    const {
      sortBy,
      filterColumn,
      filterValue,
      search,
      page = 1,
      pageSize = 10,
      status,
    } = req.query;

    let resolvedFilterValue: string | null = null;
    let resolvedFilterColumn: string | null = null;
    let resolvedStatusId: number | null = null;

    // ───── 1. Filter column logic ─────
    if (filterColumn === "created_at") {
      if (!filterValue || String(filterValue).trim() === "") {
        resolvedFilterColumn = null;
        resolvedFilterValue = null;
      } else {
        const monthNames = [
          "january",
          "february",
          "march",
          "april",
          "may",
          "june",
          "july",
          "august",
          "september",
          "october",
          "november",
          "december",
        ];
        const monthIndex = monthNames.indexOf(
          String(filterValue).toLowerCase()
        );
        if (monthIndex === -1) {
          return error(res, 400, `Invalid month filter: '${filterValue}'`);
        }

        const year = new Date().getFullYear();
        const formattedMonth = String(monthIndex + 1).padStart(2, "0");
        resolvedFilterColumn = "created_at";
        resolvedFilterValue = `${year}-${formattedMonth}`;
      }
    } else if (filterColumn && filterValue) {
      resolvedFilterColumn = String(filterColumn);
      resolvedFilterValue = String(filterValue);
    }

    // ───── 2. Independent status filter ─────
    if (status && String(status).trim().toLowerCase() !== "all status") {
      const statusName = String(status).trim();
      const { rows } = await db.query(
        `SELECT id FROM ${TABLE.STATUS} WHERE LOWER(name) = LOWER($1) LIMIT 1`,
        [statusName]
      );
      if (rows.length === 0) {
        return error(res, 400, `Status '${statusName}' not found.`);
      }
      resolvedStatusId = rows[0].id;
    }

    const sort = typeof sortBy === "string" ? sortBy : "id";
    const resolvedSearch = search ? String(search) : null;
    const resolvedPage = Number(page) || 1;
    const resolvedPageSize = Number(pageSize) || 10;

    const params = [
      1, // p_fnid
      null, // p_id
      null, // p_full_name
      null, // p_email
      null, // p_phone
      null, // p_referral_id
      null, // p_commission_rate
      resolvedStatusId, // p_statusid (used in WHERE)
      null, // p_notes
      sort, // p_sortby
      resolvedFilterColumn, // p_filter_column
      resolvedFilterValue, // p_filter_value
      resolvedSearch, // p_search
      resolvedPage, // p_page_no
      resolvedPageSize, // p_page_size
    ];

    const { rows } = await db.query(
      `SELECT * FROM look.sp_salesperson(${params
        .map((_, i) => `$${i + 1}`)
        .join(", ")})`,
      params
    );

    const result = rows[0].sp_salesperson;
    if (result.type === "error") return error(res, 400, result.message);

    return responseData(res, 200, result.message, result.data);
  }
);

// -------------------- GET SALESPERSON BY ID --------------------
export const getSalespersonById = asyncHandler(
  async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    if (!id) return error(res, 400, "Salesperson ID is required.");

    const params = [
      0, // p_fnid
      id,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
    ];

    const { rows } = await db.query(
      `SELECT * FROM look.sp_salesperson(${params
        .map((_, i) => `$${i + 1}`)
        .join(", ")})`,
      params
    );

    const result = rows[0].sp_salesperson;
    if (result.type === "error") return error(res, 400, result.message);
    if (!result.data) return error(res, 404, "Salesperson not found");

    return responseData(res, 200, result.message, result.data);
  }
);

// -------------------- CREATE SALESPERSON --------------------
export const createSalesperson = asyncHandler(
  async (req: Request, res: Response) => {
    const {
      fullName,
      email,
      phone,
      referralId,
      commissionRate,
      status,
      notes,
    } = req.body;

    // Validate required fields
    if (!fullName || !email || !referralId || !commissionRate || !status) {
      return error(
        res,
        400,
        "Missing required fields: fullName, email, referralId, commissionRate, status"
      );
    }

    if (isNaN(Number(commissionRate)) || Number(commissionRate) <= 0) {
      return error(res, 400, "commissionRate must be a positive number.");
    }

    const client = await db.connect();
    try {
      await client.query("BEGIN");

      // Email & ReferralId must be unique
      const { rows: duplicates } = await client.query(
        `SELECT id FROM look.salespersons WHERE LOWER(email) = LOWER($1) OR LOWER(referral_id) = LOWER($2) LIMIT 1`,
        [email, referralId]
      );
      if (duplicates.length > 0) {
        await client.query("ROLLBACK");
        return error(res, 400, "Email or Referral ID already exists.");
      }

      // Validate status exists
      const statusNames = [status];
      const statusCheck = await client.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );

      if (statusCheck.rowCount === 0) {
        await client.query("ROLLBACK");
        return error(res, 400, "Invalid status.");
      }

      const statusId = statusCheck.rows[0].id;

      const params = [
        2,
        null,
        fullName,
        email,
        phone,
        referralId,
        commissionRate,
        statusId,
        notes,
        "id",
      ];

      const { rows } = await client.query(
        `SELECT * FROM look.sp_salesperson(${params
          .map((_, i) => `$${i + 1}`)
          .join(", ")})`,
        params
      );

      const result = rows[0].sp_salesperson;
      if (result.type === "error") {
        await client.query("ROLLBACK");
        return error(res, 400, result.message);
      }

      await client.query("COMMIT");
      return responseData(res, 201, result.message, result.data);
    } catch (err) {
      await client.query("ROLLBACK");
      console.error("Create salesperson failed:", err);
      return error(res, 500, "Internal server error");
    } finally {
      client.release();
    }
  }
);

// -------------------- UPDATE SALESPERSON --------------------
export const updateSalesperson = asyncHandler(
  async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    const {
      fullName,
      email,
      phone,
      referralId,
      commissionRate,
      status,
      notes,
    } = req.body;

    if (!id) return error(res, 400, "Salesperson ID is required.");
    if (!fullName || !email || !referralId || !commissionRate || !status) {
      return error(
        res,
        400,
        "Missing required fields: fullName, email, referralId, commissionRate, status"
      );
    }

    if (isNaN(Number(commissionRate)) || Number(commissionRate) <= 0) {
      return error(res, 400, "commissionRate must be a positive number.");
    }

    const client = await db.connect();
    try {
      await client.query("BEGIN");

      // Must exist
      const { rows: exists } = await client.query(
        `SELECT id FROM look.salespersons WHERE id = $1`,
        [id]
      );
      if (exists.length === 0) {
        await client.query("ROLLBACK");
        return error(res, 404, "Salesperson not found.");
      }

      // Unique email/referralId (excluding self)
      const { rows: conflicts } = await client.query(
        `SELECT id FROM look.salespersons
       WHERE (LOWER(email) = LOWER($1) OR LOWER(referral_id) = LOWER($2)) AND id <> $3 LIMIT 1`,
        [email, referralId, id]
      );
      if (conflicts.length > 0) {
        await client.query("ROLLBACK");
        return error(
          res,
          400,
          "Another salesperson with this email or referralId already exists."
        );
      }

      // Check status exists
      const statusNames = [status];
      const statusCheck = await client.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );

      if (statusCheck.rowCount === 0) {
        await client.query("ROLLBACK");
        return error(res, 400, "Invalid status.");
      }

      const statusId = statusCheck.rows[0].id;

      const params = [
        2,
        id,
        fullName,
        email,
        phone,
        referralId,
        commissionRate,
        statusId,
        notes,
        "id",
      ];

      const { rows } = await client.query(
        `SELECT * FROM look.sp_salesperson(${params
          .map((_, i) => `$${i + 1}`)
          .join(", ")})`,
        params
      );

      const result = rows[0].sp_salesperson;
      if (result.type === "error") {
        await client.query("ROLLBACK");
        return error(res, 400, result.message);
      }

      await client.query("COMMIT");
      return responseData(res, 200, result.message, result.data);
    } catch (err) {
      await client.query("ROLLBACK");
      console.error("Update salesperson failed:", err);
      return error(res, 500, "Internal server error");
    } finally {
      client.release();
    }
  }
);

// -------------------- DELETE SALESPERSON --------------------
export const deleteSalesperson = asyncHandler(
  async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    if (!id) return error(res, 400, "Salesperson ID is required.");

    const client = await db.connect();
    try {
      await client.query("BEGIN");

      const params = [
        3, // fnid = delete
        id,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        "id",
      ];

      const { rows } = await client.query(
        `SELECT * FROM look.sp_salesperson(${params
          .map((_, i) => `$${i + 1}`)
          .join(", ")})`,
        params
      );

      const result = rows[0].sp_salesperson;
      if (result.type === "error") {
        await client.query("ROLLBACK");
        return error(res, 400, result.message);
      }

      await client.query("COMMIT");
      return response(res, 200, result.message);
    } catch (err) {
      await client.query("ROLLBACK");
      console.error("Delete salesperson failed:", err);
      return error(res, 500, "Internal server error");
    } finally {
      client.release();
    }
  }
);

export const updateSalespersonStatus = asyncHandler(
  async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    const { status } = req.body;

    if (!id) return error(res, 400, "Salesperson ID is required.");
    if (!status) return error(res, 400, "Status is required.");

    const client = await db.connect();
    try {
      await client.query("BEGIN");

      // Check if the salesperson exists
      const { rows: exists } = await client.query(
        `SELECT id FROM look.salespersons WHERE id = $1`,
        [id]
      );
      if (exists.length === 0) {
        await client.query("ROLLBACK");
        return error(res, 404, "Salesperson not found.");
      }

      // Validate status
      const statusNames = [status];
      const statusCheck = await client.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );

      if (statusCheck.rowCount === 0) {
        await client.query("ROLLBACK");
        return error(res, 400, "Invalid status.");
      }

      const statusId = statusCheck.rows[0].id;

      const { rows: existing } = await client.query(
        `SELECT * FROM look.salespersons WHERE id = $1`,
        [id]
      );
      const current = existing[0];

      // Send all required fields from DB with updated status
      const params = [
        2,
        id,
        current.full_name,
        current.email,
        current.phone,
        current.referral_id,
        current.commission_rate,
        statusId,
        current.notes,
        "id",
      ];

      const { rows } = await client.query(
        `SELECT * FROM look.sp_salesperson(${params
          .map((_, i) => `$${i + 1}`)
          .join(", ")})`,
        params
      );

      const result = rows[0].sp_salesperson;
      if (result.type === "error") {
        await client.query("ROLLBACK");
        return error(res, 400, result.message);
      }

      await client.query("COMMIT");
      return responseData(res, 200, result.message, result.data);
    } catch (err) {
      await client.query("ROLLBACK");
      console.error("Update salesperson status failed:", err);
      return error(res, 500, "Internal server error");
    } finally {
      client.release();
    }
  }
);
