import { Request, Response } from "express";
import { PropertyService } from "../../../service/PropertyService";
import asyncHandler from "../../../middleware/trycatch";
import { response, responseData } from "../../../utils/response";

export class PropertyController {
  private propertyService = new PropertyService();

  getAllProperties = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.propertyService.getAllProperties(req.query);
    return responseData(res, 200, "Properties fetched successfully", result);
  });

  getPropertyById = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.propertyService.getPropertyById(
      Number(req.params.id)
    );
    return responseData(res, 200, "Property fetched successfully", result);
  });

  createOrUpdateProperty = asyncHandler(async (req: Request, res: Response) => {
    try {
      const result = await this.propertyService.createOrUpdateProperty(req);
      return responseData(res, 200, "Property saved successfully", result);
    } catch (err: any) {
      console.error("Property Save Error:", err);

      return response(
        res,
        err.statusCode || 500,
        err.message || "An unexpected error occurred while saving property."
      );
    }
  });

  updateStatus = asyncHandler(async (req: Request, res: Response) => {
    await this.propertyService.updatePropertyStatus(
      Number(req.params.id),
      req.body.status
    );
    return response(res, 200, "Status updated successfully");
  });

  toggleFlag = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.propertyService.togglePropertyFlag(
      Number(req.params.id),
      req.body.column
    );
    return response(res, 200, result);
  });

  deleteProperty = asyncHandler(async (req: Request, res: Response) => {
    await this.propertyService.deleteProperty(Number(req.params.id));
    return response(res, 200, "Property deleted successfully.");
  });

  updatePhotos = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.propertyService.updatePropertyPhotos(req);
    return responseData(
      res,
      200,
      "Property photos updated successfully.",
      result
    );
  });
}
