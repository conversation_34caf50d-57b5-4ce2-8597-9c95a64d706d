import type { Knex } from "knex";


export async function up(knex: Knex): Promise<void> {
      return knex.schema.createTable("commission", function (table) {
    table.increments("id").primary();  
    table.bigInteger("userId").notNullable();  
    table.bigInteger("salesPersonId").notNullable();  
    table.bigInteger("subscriptionId").notNullable();  
    table.bigInteger("commissionAmount").nullable(); 
    table.integer("status").nullable(); 
    table.timestamp("paidAt").nullable();
    table.timestamps(true, true);
  });
}


export async function down(knex: Knex): Promise<void> {
}

