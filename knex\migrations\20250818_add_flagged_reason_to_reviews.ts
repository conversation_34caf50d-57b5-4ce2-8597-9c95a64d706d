import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('agn.reviews', (table) => {
    // Add flagged_reason column to store the reason for flagging
    table.text('flagged_reason').nullable().comment('Reason for flagging the review');
    
    // Add flagged_by column to track who flagged it
    table.integer('flagged_by').nullable().comment('ID of the user who flagged the review');
    
    // Add flagged_at timestamp
    table.timestamp('flagged_at').nullable().comment('Timestamp when the review was flagged');
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('agn.reviews', (table) => {
    table.dropColumn('flagged_reason');
    table.dropColumn('flagged_by');
    table.dropColumn('flagged_at');
  });
}
