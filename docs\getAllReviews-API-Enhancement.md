# Enhanced getAllReviews API Documentation

## Overview

The `getAllReviews` method has been enhanced to support comprehensive filtering by **status**, **search terms**, and **type** (agent vs agency reviews). This allows administrators to efficiently filter and find reviews based on multiple criteria.

## API Endpoint

```
GET /admin/reviews
```

## Query Parameters

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `page` | number | No | Page number for pagination (default: 1) | `?page=2` |
| `limit` | number | No | Number of reviews per page (default: 10) | `?limit=20` |
| `status` | string | No | Filter by review status ID | `?status=2` |
| `search` | string | No | Search in reviewer/reviewee names, emails, and agency names | `?search=john` |
| `type` | string | No | Filter by reviewee type: 'agent' or 'agency' | `?type=agent` |

## Filter Combinations

The API supports all combinations of filters:

### 1. **No Filters** (Get All)
```http
GET /admin/reviews
```
Returns all reviews with default pagination.

### 2. **Status Filter Only**
```http
GET /admin/reviews?status=30
```
Returns only approved reviews (status ID 30).

### 3. **Search Filter Only**
```http
GET /admin/reviews?search=john
```
Returns reviews where reviewer name, reviewee name, reviewer email, reviewee email, or agency name contains "john".

### 4. **Type Filter Only**
```http
GET /admin/reviews?type=agent
```
Returns only reviews for individual agents.

```http
GET /admin/reviews?type=agency
```
Returns only reviews for agencies/companies.

### 5. **Combined Filters**
```http
GET /admin/reviews?search=john&status=30&type=agent
```
Returns approved reviews for agents where search criteria matches "john".

```http
GET /admin/reviews?search=property&type=agency&page=2&limit=5
```
Returns reviews for agencies containing "property" with custom pagination.

## Response Format

```json
{
  "status": 200,
  "message": "Reviews fetched successfully",
  "data": {
    "reviews": [
      {
        "id": 1,
        "reviewerId": 123,
        "revieweeId": 456,
        "reviewText": "Excellent service!",
        "rating": 5,
        "statusId": 2,
        "statusName": "Approved",
        "flagged": false,
        "created_at": "2025-01-15T10:30:00Z",
        "updated_at": "2025-01-15T10:30:00Z",
        "reviewerFirstName": "John",
        "reviewerLastName": "Doe",
        "reviewerEmail": "<EMAIL>",
        "revieweeFirstName": "Jane",
        "revieweeLastName": "Smith",
        "revieweeEmail": "<EMAIL>",
        "revieweeType": "Individual",
        "agencyName": null,
        "notes": []
      }
    ],
    "pagination": {
      "total": 25,
      "totalPages": 3,
      "currentPage": 1,
      "perPage": 10,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

## Account Types and Type Filter

### Agent Reviews (`type=agent`)
- Filters reviews where `revieweeType = "Individual"`
- These are reviews for individual agents

### Agency Reviews (`type=agency`)
- Filters reviews where `revieweeType = "Company/Agency/PropertyDeveloper"`
- These are reviews for agencies, companies, or property developers
- Includes `agencyName` field in the response

## Search Functionality

The search filter performs case-insensitive partial matching across:

1. **Reviewer Information:**
   - First name + Last name (concatenated)
   - Email address

2. **Reviewee Information:**
   - First name + Last name (concatenated)
   - Email address

3. **Agency Information:**
   - Agency name (only for agency reviewees)

### Search Examples

```http
# Find reviews by reviewer email
GET /admin/reviews?search=<EMAIL>

# Find reviews containing "property" in any searchable field
GET /admin/reviews?search=property

# Find reviews by partial name
GET /admin/reviews?search=smith
```

## Status Codes

| Status | Description | Example Values |
|--------|-------------|----------------|
| 3 | Pending | Reviews awaiting approval |
| 30 | Approved/Confirmed | Published reviews |
| 8 | Rejected | Rejected reviews |
| 31 | Hidden/Deleted | Hidden or deleted reviews |

## Error Responses

### Invalid Type Parameter
```json
{
  "status": 400,
  "message": "Invalid type parameter. Must be 'agent' or 'agency'"
}
```

### Service Error
```json
{
  "status": 500,
  "message": "Failed to fetch reviews"
}
```

## Performance Considerations

- **No Filters**: Uses optimized predefined queries
- **With Filters**: Uses dynamic queries built based on active filters
- **Pagination**: Always included to manage large datasets
- **Indexes**: Database should have indexes on `statusId`, `reviewerId`, `revieweeId`, and `accountType` for optimal performance

## Implementation Details

### Service Layer
- Validates type parameter values
- Passes all filters to repository layer
- Returns structured response with pagination

### Repository Layer
- Dynamic query building based on active filters
- Optimized parameter handling to prevent SQL injection
- Separate count queries for accurate pagination
- Fallback to optimized queries when no filters applied

### Database Queries
- Left joins with profile and agencies tables
- Comprehensive search across multiple text fields
- Proper parameterization for security
- Consistent ordering by creation date (DESC)

## Use Cases

1. **Admin Dashboard**: Display all reviews with filtering options
2. **Review Moderation**: Filter by status to handle pending reviews
3. **Agent Management**: View reviews specific to individual agents
4. **Agency Management**: View reviews specific to agencies
5. **Search & Investigation**: Find specific reviews by user information
6. **Combined Workflows**: Complex filtering for specific business needs

## Migration Notes

This enhancement is backward compatible:
- Existing API calls without new parameters work unchanged
- New parameters are optional and ignored if not provided
- Response format remains consistent
- No breaking changes to existing functionality
