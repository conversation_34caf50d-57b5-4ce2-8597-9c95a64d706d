import db from "../config/database";
import { TABLE } from "../utils/database/table";

export class NoteRepository {
  async createNoteForProperty(
    propertyId: number,
    note: string,
    createdBy: number
  ) {
    const params = [
      10, // p_fnid (create note)
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null, // lead-related placeholders
      "property", // p_entity_type
      propertyId, // p_entity_id
      note, // p_note
      null,
      null,
      null, // sorting/filter
      1, // page
      10, // size
      null, // search
    ];

    const client = await db.connect();
    try {
      const placeholders = params.map((_, i) => `$${i + 1}`).join(", ");
      const { rows } = await client.query(
        `SELECT * FROM look.sp_leads_notes(${placeholders})`,
        params
      );

      const result = rows[0].sp_leads_notes;
      if (result.type === "error") throw new Error(result.message);

      return result.data;
    } finally {
      client.release();
    }
  }

  async getNotesByPropertyId(propertyId: number) {
    const client = await db.connect();
    try {
      await client.query("BEGIN");
      const { rows } = await client.query(
        `SELECT * FROM ${TABLE.NOTES} WHERE "entityId" = $1 AND "entityType" = 'property' ORDER BY "created_at" DESC`,
        [propertyId]
      );
      await client.query("COMMIT");
      return rows;
    } catch (err) {
      await client.query("ROLLBACK");
      throw err;
    } finally {
      client.release();
    }
  }
}
