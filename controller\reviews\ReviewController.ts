import { Request, Response } from "express";
import { ReviewService } from "../../service/ReviewsService";
import async<PERSON>and<PERSON> from "../../middleware/trycatch";
import { response, responseData } from "../../utils/response";

interface AuthenticatedRequest extends Request {
  user?: {
    id: number;
    [key: string]: any;
  };
}

export class ReviewController {
  private reviewService = new ReviewService();

  createReview = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const reviewerId = req.user?.id;
    if (!reviewerId) {
      return response(res, 401, "Unauthorized: Agent not logged in.");
    }

    const { revieweeId, reviewText, rating } = req.body;
    
    
    if (!revieweeId || !reviewText || !rating) {
      return response(res, 400, "Missing required fields: revieweeId, reviewText, and rating");
    }

    if (reviewerId === parseInt(revieweeId)) {
      return response(res, 400, "You cannot review yourself");
    }

    try {
      const result = await this.reviewService.createReview(reviewerId, revieweeId, reviewText, rating);
      return responseData(res, 201, "Review created successfully", result);
    } catch (error: any) {
      if (error.message === "You have already reviewed this profile") {
        return response(res, 409, "You have already reviewed this profile");
      }
      if (error.message === "Rating must be between 1 and 5") {
        return response(res, 400, "Rating must be between 1 and 5");
      }
      if (error.message === "Reviewee not found") {
        return response(res, 404, "User not found");
      }
      if (error.message === "You can only review agents or agencies") {
        return response(res, 400, "You can only review agents or agencies");
      }
      if (error.message === "You cannot review yourself") {
        return response(res, 400, "You cannot review yourself");
      }
      
      console.error("Error creating review:", error);
      return response(res, 500, "Something went wrong while creating review");
    }
  });
}